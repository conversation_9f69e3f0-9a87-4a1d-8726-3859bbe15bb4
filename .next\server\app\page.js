(()=>{var e={};e.id=974,e.ids=[974],e.modules={124:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(687),i=n(3210),a=n(9877),s=n(5814),o=n.n(s);let l=()=>{let[e,t]=(0,i.useState)(!1),[n,s]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{t(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let l=[{name:"Home",href:"/"},{name:"Cases",href:"/cases"},{name:"About",href:"/about"},{name:"Blog",href:"/article"},{name:"Contact",href:"/contact"}];return(0,r.jsx)(a.P.header,{initial:{y:-100},animate:{y:0},transition:{duration:.6,ease:"easeOut"},className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${e?"bg-white/95 backdrop-blur-md shadow-lg":"bg-transparent"}`,children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,r.jsx)(a.P.div,{whileHover:{scale:1.05},transition:{duration:.2},children:(0,r.jsxs)(o(),{href:"/",className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-black rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"P"})}),(0,r.jsx)("span",{className:"ml-3 text-xl font-bold font-display text-black",children:"Panda Patronage"})]})}),(0,r.jsx)("nav",{className:"hidden lg:flex items-center space-x-8",children:l.map((e,t)=>(0,r.jsx)(a.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},children:(0,r.jsx)(o(),{href:e.href,className:"text-gray-700 hover:text-black transition-colors duration-200 font-medium",children:e.name})},e.name))}),(0,r.jsx)(a.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.3},className:"hidden lg:block",children:(0,r.jsx)(o(),{href:"/contact",className:"bg-black text-white px-6 py-3 rounded-full font-medium hover:bg-gray-800 transition-colors duration-200",children:"Let's Talk"})}),(0,r.jsx)("button",{onClick:()=>s(!n),className:"lg:hidden p-2",children:(0,r.jsxs)("div",{className:"w-6 h-6 flex flex-col justify-center items-center",children:[(0,r.jsx)("span",{className:`block w-6 h-0.5 bg-black transition-all duration-300 ${n?"rotate-45 translate-y-1":""}`}),(0,r.jsx)("span",{className:`block w-6 h-0.5 bg-black mt-1 transition-all duration-300 ${n?"opacity-0":""}`}),(0,r.jsx)("span",{className:`block w-6 h-0.5 bg-black mt-1 transition-all duration-300 ${n?"-rotate-45 -translate-y-1":""}`})]})})]}),(0,r.jsx)(a.P.div,{initial:{opacity:0,height:0},animate:{opacity:+!!n,height:n?"auto":0},transition:{duration:.3},className:"lg:hidden overflow-hidden bg-white border-t",children:(0,r.jsxs)("div",{className:"py-4 space-y-4",children:[l.map(e=>(0,r.jsx)(o(),{href:e.href,className:"block px-4 py-2 text-gray-700 hover:text-black transition-colors duration-200",onClick:()=>s(!1),children:e.name},e.name)),(0,r.jsx)("div",{className:"px-4",children:(0,r.jsx)(o(),{href:"/contact",className:"block w-full text-center bg-black text-white px-6 py-3 rounded-full font-medium hover:bg-gray-800 transition-colors duration-200",onClick:()=>s(!1),children:"Let's Talk"})})]})})]})})}},195:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return o},urlObjectKeys:function(){return s}});let r=n(740)._(n(6715)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",s=e.pathname||"",o=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+o}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return a(e)}},221:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\panda-patronage\\\\components\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\panda-patronage\\components\\Hero.tsx","default")},440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var r=n(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function n(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return n}})},593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return h},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return s},navigate:function(){return i},prefetch:function(){return r},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return o}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,i=n,a=n,s=n,o=n,l=n,u=n,c=n;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),h=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},642:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],a=Array.isArray(t),s=a?t[1]:t;!s||s.startsWith(i.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):a&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(2859),i=n(3913),a=n(4077),s=e=>"/"===e[0]?e.slice(1):e,o=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=s(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===i.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(i.PAGE_SEGMENT_KEY))return"";let a=[o(n)],s=null!=(t=e[1])?t:{},c=s.children?u(s.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(s)){if("children"===e)continue;let n=u(t);void 0!==n&&a.push(n)}return l(a)}function c(e,t){let n=function e(t,n){let[i,s]=t,[l,c]=n,d=o(i),h=o(l);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||h.startsWith(e)))return"";if(!(0,a.matchSegment)(i,l)){var f;return null!=(f=u(n))?f:""}for(let t in s)if(c[t]){let n=e(s[t],c[t]);if(null!==n)return o(l)+"/"+n}return null}(e,t);return null==n||"/"===n?n:l(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},660:(e,t)=>{"use strict";function n(e){let t=5381;for(let n=0;n<e.length;n++)t=(t<<5)+t+e.charCodeAt(n)|0;return t>>>0}function r(e){return n(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{djb2Hash:function(){return n},hexHash:function(){return r}})},793:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\panda-patronage\\\\components\\\\Testimonials.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\panda-patronage\\components\\Testimonials.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>h});var r=n(7413),i=n(7182),a=n(221),s=n(2385),o=n(1245),l=n(793),u=n(2175),c=n(6266),d=n(6920);function h(){return(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsx)(i.default,{}),(0,r.jsxs)("main",{children:[(0,r.jsx)(a.default,{}),(0,r.jsx)(s.default,{}),(0,r.jsx)(o.default,{}),(0,r.jsx)(l.default,{}),(0,r.jsx)(u.default,{}),(0,r.jsx)(c.default,{})]}),(0,r.jsx)(d.default,{})]})}},1245:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\panda-patronage\\\\components\\\\Portfolio.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\panda-patronage\\components\\Portfolio.tsx","default")},1279:(e,t,n)=>{"use strict";n.d(t,{t:()=>r});let r=(0,n(3210).createContext)(null)},1437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return a}});let r=n(4722),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function s(e){let t,n,a;for(let r of e.split("/"))if(n=i.find(e=>r.startsWith(e))){[t,a]=e.split(n,2);break}if(!t||!n||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,r.normalizeAppPath)(t),n){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=s.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},1500:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,a,s,o,l,u){if(0===Object.keys(s[1]).length){n.head=l;return}for(let c in s[1]){let d,h=s[1][c],f=h[0],p=(0,r.createRouterCacheKey)(f),m=null!==o&&void 0!==o[2][c]?o[2][c]:null;if(a){let r=a.parallelRoutes.get(c);if(r){let a,s=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,o=new Map(r),d=o.get(p);a=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:s&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},o.set(p,a),e(t,a,d,h,m||null,l,u),n.parallelRoutes.set(c,o);continue}}if(null!==m){let e=m[1],n=m[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=n.parallelRoutes.get(c);g?g.set(p,d):n.parallelRoutes.set(c,new Map([[p,d]])),e(t,d,void 0,h,m,l,u)}}}});let r=n(3123),i=n(9154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1550:(e,t)=>{"use strict";function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},1658:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillMetadataSegment:function(){return h},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return f}});let r=n(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(n(8671)),a=n(6341),s=n(4396),o=n(660),l=n(4722),u=n(2958),c=n(5499);function d(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let n="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(n=(0,o.djb2Hash)(t).toString(36).slice(0,6)),n}function h(e,t,n){let r=(0,l.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),c=(0,a.interpolateDynamicPath)(r,t,o),{name:h,ext:f}=i.default.parse(n),p=d(i.default.posix.join(e,h)),m=p?`-${p}`:"";return(0,u.normalizePathSep)(i.default.join(c,`${h}${m}${f}`))}function f(e){if(!(0,r.isMetadataPage)(e))return e;let t=e,n="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":n=d(e),!t.endsWith("/route")){let{dir:e,name:r,ext:a}=i.default.parse(t);t=i.default.posix.join(e,`${r}${n?`-${n}`:""}${a}`,"route")}return t}function p(e,t){let n=e.endsWith("/route"),r=n?e.slice(0,-6):e,i=r.endsWith("/sitemap")?".xml":"";return(t?`${r}/[__metadata_id__]`:`${r}${i}`)+(n?"/route":"")}},1794:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(9289),i=n(6736);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},2011:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6444,23)),Promise.resolve().then(n.t.bind(n,6042,23)),Promise.resolve().then(n.t.bind(n,8170,23)),Promise.resolve().then(n.t.bind(n,9477,23)),Promise.resolve().then(n.t.bind(n,9345,23)),Promise.resolve().then(n.t.bind(n,2089,23)),Promise.resolve().then(n.t.bind(n,6577,23)),Promise.resolve().then(n.t.bind(n,1307,23))},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],i=n[0];if(Array.isArray(r)&&Array.isArray(i)){if(r[0]!==i[0]||r[2]!==i[2])return!0}else if(r!==i)return!0;if(t[4])return!n[4];if(n[4])return!0;let a=Object.values(t[1])[0],s=Object.values(n[1])[0];return!a||!s||e(a,s)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2157:(e,t,n)=>{"use strict";n.d(t,{L:()=>r});let r=(0,n(3210).createContext)({})},2175:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\panda-patronage\\\\components\\\\Pricing.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\panda-patronage\\components\\Pricing.tsx","default")},2255:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let r=n(1550);function i(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},2285:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(687),i=n(9877),a=n(8265),s=n(3210),o=n(5814),l=n.n(o);let u=()=>{let e=(0,s.useRef)(null),t=(0,a.W)(e,{once:!0,margin:"-100px"}),n={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2,delayChildren:.1}}},o={hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.8,ease:"easeOut"}}};return(0,r.jsx)("section",{ref:e,className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(i.P.div,{variants:n,initial:"hidden",animate:t?"visible":"hidden",className:"text-center mb-16",children:[(0,r.jsx)(i.P.span,{variants:o,className:"text-sm font-medium text-gray-500 uppercase tracking-wider",children:"Simple Pricing"}),(0,r.jsx)(i.P.h2,{variants:o,className:"text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mt-4",children:"Unlock Your Growth"})]}),(0,r.jsx)(i.P.div,{variants:n,initial:"hidden",animate:t?"visible":"hidden",className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto",children:[{name:"Part-time",price:"$2450",period:"/ month",features:["Fast delivery & response","Pause / cancel anytime","Dedicated project manager","1 request at the time","SEO Marketing"],popular:!1,href:"https://stylokit.lemonsqueezy.com/buy/e842c934-9e86-4005-9608-8a025089e6a4"},{name:"Full-time",price:"$3950",period:"/ month",features:["Fast delivery & response","Pause / cancel anytime","Dedicated project manager","10 request at the time","E-commerce integration"],popular:!1,href:"https://stylokit.lemonsqueezy.com/buy/e842c934-9e86-4005-9608-8a025089e6a4"},{name:"Custom",price:"$5999",period:"/ month",features:["Fast delivery & response","Pause / cancel anytime","Dedicated project manager","Unlimited request","E-commerce integration"],popular:!0,href:"https://stylokit.lemonsqueezy.com/buy/e842c934-9e86-4005-9608-8a025089e6a4"}].map((e,t)=>(0,r.jsxs)(i.P.div,{variants:o,whileHover:{y:-10,scale:1.02},transition:{duration:.3},className:`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 ${e.popular?"ring-2 ring-black":""}`,children:[e.popular&&(0,r.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,r.jsx)("span",{className:"bg-black text-white px-4 py-2 rounded-full text-sm font-medium",children:"Popular"})}),(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-bold font-display text-black mb-4",children:e.name}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("span",{className:"text-4xl font-bold text-black",children:e.price}),(0,r.jsx)("span",{className:"text-gray-600 ml-2",children:e.period})]}),(0,r.jsx)("ul",{className:"space-y-4 mb-8",children:e.features.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},t))}),(0,r.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(l(),{href:e.href,target:"_blank",rel:"noopener noreferrer",className:`block w-full text-center py-3 px-6 rounded-full font-medium transition-all duration-300 ${e.popular?"bg-black text-white hover:bg-gray-800":"bg-gray-100 text-black hover:bg-gray-200"}`,children:"Get Started"})})]})]},e.name))}),(0,r.jsxs)(i.P.div,{variants:o,initial:"hidden",animate:t?"visible":"hidden",className:"text-center mt-12",children:[(0,r.jsx)("p",{className:"text-gray-600",children:"All plans include 24/7 support and a 30-day money-back guarantee."}),(0,r.jsxs)("p",{className:"text-gray-600 mt-2",children:["Need a custom solution?"," ",(0,r.jsx)(l(),{href:"/contact",className:"text-black font-medium hover:underline",children:"Contact us"})]})]})]})})}},2308:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,i,,s]=t;for(let o in r.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==s&&(t[2]=n,t[3]="refresh"),i)e(i[o],n)}},refreshInactiveParallelSegments:function(){return s}});let r=n(6928),i=n(9008),a=n(3913);async function s(e){let t=new Set;await o({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function o(e){let{navigatedAt:t,state:n,updatedTree:a,updatedCache:s,includeNextUrl:l,fetchedSegments:u,rootTree:c=a,canonicalUrl:d}=e,[,h,f,p]=a,m=[];if(f&&f!==d&&"refresh"===p&&!u.has(f)){u.add(f);let e=(0,i.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,s,s,e)});m.push(e)}for(let e in h){let r=o({navigatedAt:t,state:n,updatedTree:h[e],updatedCache:s,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:d});m.push(r)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2385:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\panda-patronage\\\\components\\\\Services.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\panda-patronage\\components\\Services.tsx","default")},2437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let r=n(5362);function i(e,t){let n=[],i=(0,r.pathToRegexp)(e,n,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,r.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,n);return(e,r)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of n)"number"==typeof e.name&&delete i.params[e.name];return{...r,...i.params}}}},2582:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});let r=(0,n(3210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},2675:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var r=n(5239),i=n(8088),a=n(8170),s=n.n(a),o=n(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,1204)),"C:\\laragon\\www\\panda-patronage\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,4431)),"C:\\laragon\\www\\panda-patronage\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\laragon\\www\\panda-patronage\\src\\app\\page.tsx"],d={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},2708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},2743:(e,t,n)=>{"use strict";n.d(t,{E:()=>i});var r=n(3210);let i=n(7044).B?r.useLayoutEffect:r.useEffect},2785:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},2789:(e,t,n)=>{"use strict";n.d(t,{M:()=>i});var r=n(3210);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2899:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(687),i=n(9877),a=n(8265),s=n(3210);let o=()=>{let e=(0,s.useRef)(null),t=(0,a.W)(e,{once:!0,margin:"-100px"}),[n,o]=(0,s.useState)(0),l={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2,delayChildren:.1}}},u={hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.8,ease:"easeOut"}}},c=[{name:"Elite Fitness Co.",role:"Owner",content:"Working with Panda Patronage transformed our brand&apos;s online presence. Their innovative strategies increased our website traffic by 45% in just 3 months!",avatar:"/api/placeholder/60/60",rating:5},{name:"GreenTech Solutions",role:"Manager",content:"Panda Patronage took our ad campaigns to the next level. Their full-funnel approach helped us achieve a 200% ROI on our marketing spend.",avatar:"/api/placeholder/60/60",rating:5},{name:"Harmony Skin Care",role:"CEO",content:"We struggled with brand consistency across platforms until their team created a cohesive strategy that boosted customer engagement.",avatar:"/api/placeholder/60/60",rating:5}];return(0,r.jsx)("section",{ref:e,className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(i.P.div,{variants:l,initial:"hidden",animate:t?"visible":"hidden",className:"text-center mb-16",children:[(0,r.jsx)(i.P.span,{variants:u,className:"text-sm font-medium text-gray-500 uppercase tracking-wider",children:"Testimonials"}),(0,r.jsx)(i.P.h2,{variants:u,className:"text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mt-4 mb-6",children:"Our satisfied customers"}),(0,r.jsx)(i.P.p,{variants:u,className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Panda Patronage empowers teams with seamless Strategies and time-saving Solutions. Discover client success!"})]}),(0,r.jsx)(i.P.div,{variants:l,initial:"hidden",animate:t?"visible":"hidden",className:"mb-20",children:(0,r.jsxs)("div",{className:"relative max-w-4xl mx-auto",children:[(0,r.jsx)(i.P.div,{variants:u,className:"bg-gray-50 rounded-3xl p-8 md:p-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(i.P.svg,{className:"w-6 h-6 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.1*t+.5},children:(0,r.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},t))}),(0,r.jsxs)(i.P.blockquote,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-xl md:text-2xl font-medium text-gray-800 mb-8 leading-relaxed",children:['"',c[n].content,'"']},n),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-600 font-medium",children:c[n].name.charAt(0)})}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"font-semibold text-gray-800",children:c[n].role}),(0,r.jsxs)("p",{className:"text-gray-600",children:["– ",c[n].name]})]})]})]})}),(0,r.jsx)("div",{className:"flex justify-center mt-8 space-x-3",children:c.map((e,t)=>(0,r.jsx)("button",{onClick:()=>o(t),className:`w-3 h-3 rounded-full transition-all duration-300 ${t===n?"bg-black scale-125":"bg-gray-300 hover:bg-gray-400"}`},t))})]})}),(0,r.jsx)(i.P.div,{variants:l,initial:"hidden",animate:t?"visible":"hidden",className:"border-t border-gray-200 pt-16",children:(0,r.jsx)(i.P.div,{variants:u,className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60",children:[{name:"Brand 1",logo:"/api/placeholder/120/60"},{name:"Brand 2",logo:"/api/placeholder/120/60"},{name:"Brand 3",logo:"/api/placeholder/120/60"},{name:"Brand 4",logo:"/api/placeholder/120/60"},{name:"Brand 5",logo:"/api/placeholder/120/60"},{name:"Brand 6",logo:"/api/placeholder/120/60"},{name:"Brand 7",logo:"/api/placeholder/120/60"},{name:"Brand 8",logo:"/api/placeholder/120/60"}].map((e,t)=>(0,r.jsx)(i.P.div,{variants:u,whileHover:{scale:1.1,opacity:1},transition:{duration:.2},className:"flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500 font-medium text-sm",children:e.name})})},e.name))})})]})})}},2958:(e,t)=>{"use strict";function n(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return n}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(3210);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=a(e,r)),t&&(i.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function i(e){return n.test(e)?e.replace(r,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3406:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return b},onNavigationIntent:function(){return P},pingVisibleLinks:function(){return j},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return x}}),n(3690);let r=n(9752),i=n(9154),a=n(593),s=n(3210),o=null,l={pending:!0},u={pending:!1};function c(e){(0,s.startTransition)(()=>{null==o||o.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),o=e})}function d(e){o===e&&(o=null)}let h="function"==typeof WeakMap?new WeakMap:new Map,f=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;b(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==h.get(e)&&x(e),h.set(e,t),null!==p&&p.observe(e)}function g(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,n,r,i,a){if(i){let i=g(t);if(null!==i){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:a};return m(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,n,r){let i=g(t);null!==i&&m(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function x(e){let t=h.get(e);if(void 0!==t){h.delete(e),f.delete(t);let n=t.prefetchTask;null!==n&&(0,a.cancelPrefetchTask)(n)}null!==p&&p.unobserve(e)}function b(e,t){let n=h.get(e);void 0!==n&&(n.isVisible=t,t?f.add(n):f.delete(n),w(n))}function P(e,t){let n=h.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,w(n))}function w(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function j(e,t){let n=(0,a.getCurrentCacheVersion)();for(let r of f){let s=r.prefetchTask;if(null!==s&&r.cacheVersion===n&&s.key.nextUrl===e&&s.treeAtTimeOfPrefetch===t)continue;null!==s&&(0,a.cancelPrefetchTask)(s);let o=(0,a.createCacheKey)(r.prefetchHref,e),l=r.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;r.prefetchTask=(0,a.schedulePrefetchTask)(o,t,r.kind===i.PrefetchKind.FULL,l),r.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3591:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(687),i=n(9877),a=n(8265),s=n(3210);let o=()=>{let e=(0,s.useRef)(null),t=(0,a.W)(e,{once:!0,margin:"-100px"}),n={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2,delayChildren:.1}}},o={hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.8,ease:"easeOut"}}};return(0,r.jsx)("section",{ref:e,className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(i.P.div,{variants:n,initial:"hidden",animate:t?"visible":"hidden",className:"text-center mb-20",children:[(0,r.jsx)(i.P.h2,{variants:o,className:"text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mb-6",children:"Explore What Sets Us Apart"}),(0,r.jsx)(i.P.p,{variants:o,className:"text-lg text-gray-600 max-w-3xl mx-auto mb-16",children:"Explore how we stand out with innovative solutions designed to raise your fulfillment"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[{title:"Planning Phase",description:"We map out the game plan upfront, deciding what needs to be done and when, so every step is clear and focused.",icon:"\uD83D\uDCCB",color:"from-blue-400 to-blue-600"},{title:"Strategy Blueprint",description:"Once the plan is set, we craft a powerful strategy designed to drive your success and fuel your growth.",icon:"\uD83C\uDFAF",color:"from-purple-400 to-purple-600"},{title:"Working Process",description:"With the plan and strategy in place, our team dive in wholeheartedly to propel your success.",icon:"⚡",color:"from-orange-400 to-orange-600"}].map((e,t)=>(0,r.jsxs)(i.P.div,{variants:o,whileHover:{y:-10,scale:1.02},transition:{duration:.3},className:"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100",children:[(0,r.jsx)("div",{className:`w-16 h-16 rounded-full bg-gradient-to-r ${e.color} flex items-center justify-center text-2xl mb-6 mx-auto`,children:e.icon}),(0,r.jsx)("h3",{className:"text-xl font-bold font-display text-black mb-4",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]},e.title))})]}),(0,r.jsxs)(i.P.div,{variants:n,initial:"hidden",animate:t?"visible":"hidden",className:"mt-32",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)(i.P.span,{variants:o,className:"text-sm font-medium text-gray-500 uppercase tracking-wider",children:"Our services"}),(0,r.jsx)(i.P.h2,{variants:o,className:"text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mt-4 mb-6",children:"Professional Services That Showcase Our Expertise."}),(0,r.jsx)(i.P.p,{variants:o,className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"From creative design to technical solutions, our services define industry excellence."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{title:"Branding",description:"Your company&apos;s identity hinges on branding. We assist in shaping a distinct voice and identity.",image:"/api/placeholder/300/200"},{title:"Social Media Management",description:"Your audience resides on social media, and we ensure your presence there! Our social media management services maintain your profiles",image:"/api/placeholder/300/200"},{title:"Meta Advertisement",description:"Target the right audience and maximize ROI with our Meta ad campaigns on Fb and Instagram.",image:"/api/placeholder/300/200"},{title:"Graphic Designing",description:"Our visual design solutions generate powerful imagery, from logos to promotional resources, that deliver your message effectively.",image:"/api/placeholder/300/200"},{title:"Web Development",description:"Your website is your digital storefront—We designed to captivate, engage, and drive traffic.",image:"/api/placeholder/300/200"}].map((e,t)=>(0,r.jsx)(i.P.div,{variants:o,whileHover:{y:-5},transition:{duration:.3},className:"group cursor-pointer",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsxs)("div",{className:"h-48 bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-all duration-300"}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-16 h-16 bg-black rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"P"})})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold font-display text-black mb-3",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]})]})},e.title))})]})]})})}},3690:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return x}});let r=n(9154),i=n(8830),a=n(3210),s=n(1992);n(593);let o=n(9129),l=n(6127),u=n(9752),c=n(5076),d=n(3406);function h(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?f({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function f(e){let{actionQueue:t,action:n,setState:r}=e,i=t.state;t.pending=n;let a=n.payload,o=t.action(i,a);function l(e){n.discarded||(t.state=e,h(t,r),n.resolve(e))}(0,s.isThenable)(o)?o.then(l,e=>{h(t,r),n.reject(e)}):l(o)}function p(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let i={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,a.startTransition)(()=>{n(e)})}let s={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=s,f({actionQueue:e,action:s,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,s.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),f({actionQueue:e,action:s,setState:n})):(null!==e.last&&(e.last.next=s),e.last=s)})(n,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function m(){return null}function g(){return null}function y(e,t,n,i){let a=new URL((0,l.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(i);(0,o.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:a,isExternalUrl:(0,u.isExternalURL)(a),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function v(e,t){(0,o.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let x={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var a;(0,c.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:i,kind:null!=(a=null==t?void 0:t.kind)?a:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,o.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),n(4827);let r=n(2785);function i(e,t,n){void 0===n&&(n=!0);let i=new URL("http://n"),a=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:s,searchParams:o,search:l,hash:u,href:c,origin:d}=new URL(e,a);if(d!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:n?(0,r.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:c.slice(d.length)}}},3873:e=>{"use strict";e.exports=require("path")},3898:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let r=n(4400),i=n(1500),a=n(3123),s=n(3913);function o(e,t,n,o,l,u){let{segmentPath:c,seedData:d,tree:h,head:f}=o,p=t,m=n;for(let t=0;t<c.length;t+=2){let n=c[t],o=c[t+1],g=t===c.length-2,y=(0,a.createRouterCacheKey)(o),v=m.parallelRoutes.get(n);if(!v)continue;let x=p.parallelRoutes.get(n);x&&x!==v||(x=new Map(v),p.parallelRoutes.set(n,x));let b=v.get(y),P=x.get(y);if(g){if(d&&(!P||!P.lazyData||P===b)){let t=d[0],n=d[1],a=d[3];P={lazyData:null,rsc:u||t!==s.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:u&&b?new Map(b.parallelRoutes):new Map,navigatedAt:e},b&&u&&(0,r.invalidateCacheByRouterState)(P,b,h),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,P,b,h,d,f,l),x.set(y,P)}continue}P&&b&&(P===b&&(P={lazyData:P.lazyData,rsc:P.rsc,prefetchRsc:P.prefetchRsc,head:P.head,prefetchHead:P.prefetchHead,parallelRoutes:new Map(P.parallelRoutes),loading:P.loading},x.set(y,P)),p=P,m=b)}}function l(e,t,n,r,i){o(e,t,n,r,i,!0)}function u(e,t,n,r,i){o(e,t,n,r,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4183:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(687),i=n(9877),a=n(5814),s=n.n(a);let o=()=>{let e={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.8,ease:"easeOut"}}},t={animate:{y:[-10,10,-10],rotate:[-2,2,-2],transition:{duration:6,repeat:1/0,ease:"easeInOut"}}};return(0,r.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-50 to-white",children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)(i.P.div,{variants:t,animate:"animate",className:"absolute top-20 left-10 w-20 h-20 bg-yellow-400 rounded-full opacity-20"}),(0,r.jsx)(i.P.div,{variants:t,animate:"animate",style:{animationDelay:"2s"},className:"absolute top-40 right-20 w-16 h-16 bg-blue-400 rounded-full opacity-20"}),(0,r.jsx)(i.P.div,{variants:t,animate:"animate",style:{animationDelay:"4s"},className:"absolute bottom-40 left-20 w-24 h-24 bg-purple-400 rounded-full opacity-20"})]}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20",children:(0,r.jsxs)(i.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2,delayChildren:.3}}},initial:"hidden",animate:"visible",className:"text-center",children:[(0,r.jsxs)(i.P.h1,{variants:e,className:"text-4xl sm:text-5xl lg:text-7xl font-bold font-display text-black leading-tight",children:["We're"," ",(0,r.jsxs)(i.P.span,{className:"relative inline-block",whileHover:{scale:1.05},transition:{duration:.2},children:[(0,r.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent",children:"Creative"}),(0,r.jsx)(i.P.div,{className:"absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full",initial:{scaleX:0},animate:{scaleX:1},transition:{duration:1,delay:1}})]}),(0,r.jsx)("br",{}),"Digital Marketing Agency!"]}),(0,r.jsx)(i.P.p,{variants:e,className:"mt-6 text-lg sm:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed",children:"We're here to help you see through the eyes of your audience, paving the way for your success without limits."}),(0,r.jsxs)(i.P.div,{variants:e,className:"mt-10 flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,r.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(s(),{href:"/contact",className:"bg-black text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl",children:"Schedule a call"})}),(0,r.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(s(),{href:"/about",className:"border-2 border-black text-black px-8 py-4 rounded-full font-medium text-lg hover:bg-black hover:text-white transition-all duration-300",children:"Learn More"})})]}),(0,r.jsx)(i.P.div,{variants:e,className:"mt-16 relative",children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[1,2,3,4,5,6].map(e=>(0,r.jsx)(i.P.div,{variants:t,animate:"animate",style:{animationDelay:`${.5*e}s`},className:"relative group",children:(0,r.jsx)("div",{className:"w-full h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg shadow-lg group-hover:shadow-xl transition-shadow duration-300 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-16 h-16 bg-black rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"P"})})})},e))})}),(0,r.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:2,duration:1},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,r.jsx)(i.P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center",children:(0,r.jsx)(i.P.div,{animate:{y:[0,12,0]},transition:{duration:2,repeat:1/0},className:"w-1 h-3 bg-gray-400 rounded-full mt-2"})})})]})})]})}},4396:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return d},parseParameter:function(){return l}});let r=n(6143),i=n(1437),a=n(3293),s=n(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:t}}function c(e,t,n){let r={},l=1,c=[];for(let d of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),s=d.match(o);if(e&&s&&s[2]){let{key:t,optional:n,repeat:i}=u(s[2]);r[t]={pos:l++,repeat:i,optional:n},c.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:i}=u(s[2]);r[e]={pos:l++,repeat:t,optional:i},n&&s[1]&&c.push("/"+(0,a.escapeStringRegexp)(s[1]));let o=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";n&&s[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,a.escapeStringRegexp)(d));t&&s&&s[3]&&c.push((0,a.escapeStringRegexp)(s[3]))}return{parameterizedRoute:c.join(""),groups:r}}function d(e,t){let{includeSuffix:n=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:s}=c(e,n,r),o=a;return i||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function h(e){let t,{interceptionMarker:n,getSafeRouteKey:r,segment:i,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:h}=u(i),f=c.replace(/\W/g,"");o&&(f=""+o+f);let p=!1;(0===f.length||f.length>30)&&(p=!0),isNaN(parseInt(f.slice(0,1)))||(p=!0),p&&(f=r());let m=f in s;o?s[f]=""+o+c:s[f]=c;let g=n?(0,a.escapeStringRegexp)(n):"";return t=m&&l?"\\k<"+f+">":h?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function f(e,t,n,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),f={},p=[];for(let c of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),s=c.match(o);if(e&&s&&s[2])p.push(h({getSafeRouteKey:d,interceptionMarker:s[1],segment:s[2],routeKeys:f,keyPrefix:t?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(s&&s[2]){l&&s[1]&&p.push("/"+(0,a.escapeStringRegexp)(s[1]));let e=h({getSafeRouteKey:d,segment:s[2],routeKeys:f,keyPrefix:t?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&s[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,a.escapeStringRegexp)(c));n&&s&&s[3]&&p.push((0,a.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:f}}function p(e,t){var n,r,i;let a=f(e,t.prefixRouteKeys,null!=(n=t.includeSuffix)&&n,null!=(r=t.includePrefix)&&r,null!=(i=t.backreferenceDuplicateKeys)&&i),s=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...d(e,t),namedRegex:"^"+s+"$",routeKeys:a.routeKeys}}function m(e,t){let{parameterizedRoute:n}=c(e,!1,!1),{catchAll:r=!0}=t;if("/"===n)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:i}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(r?"(?:(/.*)?)":"")+"$"}}},4397:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let r=n(3123);function i(e,t){return function e(t,n,i){if(0===Object.keys(n).length)return[t,i];if(n.children){let[a,s]=n.children,o=t.parallelRoutes.get("children");if(o){let t=(0,r.createRouterCacheKey)(a),n=o.get(t);if(n){let r=e(n,s,i+"/"+t);if(r)return r}}}for(let a in n){if("children"===a)continue;let[s,o]=n[a],l=t.parallelRoutes.get(a);if(!l)continue;let u=(0,r.createRouterCacheKey)(s),c=l.get(u);if(!c)continue;let d=e(c,o,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4400:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let r=n(3123);function i(e,t,n){for(let i in n[1]){let a=n[1][i][0],s=(0,r.createRouterCacheKey)(a),o=t.parallelRoutes.get(i);if(o){let t=new Map(o);t.delete(s),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>u,metadata:()=>l});var r=n(7413),i=n(5759),a=n.n(i),s=n(6729),o=n.n(s);n(1135);let l={title:"Panda Patronage - Creative Digital Marketing Agency",description:"We're here to help you see through the eyes of your audience, paving the way for your success without limits."};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:e})})}},4479:(e,t,n)=>{"use strict";function r(e){return"object"==typeof e&&null!==e}n.d(t,{G:()=>r})},4551:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(687),i=n(9877),a=n(8265),s=n(3210),o=n(5814),l=n.n(o);let u=()=>{let e=(0,s.useRef)(null),t=(0,a.W)(e,{once:!0,margin:"-100px"}),n={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},o={hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.8,ease:"easeOut"}}};return(0,r.jsx)("section",{ref:e,className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)(i.P.div,{variants:n,initial:"hidden",animate:t?"visible":"hidden",className:"text-center mb-20",children:(0,r.jsx)(i.P.p,{variants:o,className:"text-2xl sm:text-3xl lg:text-4xl font-medium text-gray-700 max-w-5xl mx-auto leading-relaxed",children:"We have been creating projects that remain relevant today, tomorrow, and for decades to come"})}),(0,r.jsxs)(i.P.div,{variants:n,initial:"hidden",animate:t?"visible":"hidden",className:"text-center mb-16",children:[(0,r.jsx)(i.P.h2,{variants:o,className:"text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mb-6",children:"Building Digital Excellence with Panda."}),(0,r.jsx)(i.P.p,{variants:o,className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Discover the innovative marketing strategies that set Neutra apart, driving success in the digital landscape."})]}),(0,r.jsx)(i.P.div,{variants:n,initial:"hidden",animate:t?"visible":"hidden",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{title:"Codify",tags:["Agency","Portfolio","Saas"],href:"https://codify.framer.website/",color:"from-blue-500 to-purple-600"},{title:"Taskify",tags:["Business","AI","Saas"],href:"https://taskify.framer.website/",color:"from-green-500 to-teal-600"},{title:"Flexify",tags:["Saas","AI","Business"],href:"https://flexify.framer.website/",color:"from-purple-500 to-pink-600"},{title:"Landify",tags:["Business","Portfolio","Landing"],href:"https://landify.framer.website/",color:"from-orange-500 to-red-600"},{title:"Nexus AI",tags:["AI","Saas","Business"],href:"https://nexusai.framer.website/",color:"from-cyan-500 to-blue-600"},{title:"Todofusion",tags:["AI","Business","Agency"],href:"https://todofusion.framer.website/",color:"from-yellow-500 to-orange-600"}].map((e,t)=>(0,r.jsx)(i.P.div,{variants:o,whileHover:{y:-10,scale:1.02},transition:{duration:.3},className:"group",children:(0,r.jsx)(l(),{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"block",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsxs)("div",{className:`h-64 bg-gradient-to-br ${e.color} relative overflow-hidden`,children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-all duration-300"}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)(i.P.div,{animate:{y:[-5,5,-5],rotate:[-2,2,-2]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"w-20 h-20 bg-white/20 rounded-2xl backdrop-blur-sm flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:e.title.charAt(0)})})}),(0,r.jsx)(i.P.div,{animate:{x:[-10,10,-10],y:[-5,5,-5]},transition:{duration:6,repeat:1/0,ease:"easeInOut"},className:"absolute top-4 right-4 w-8 h-8 bg-white/30 rounded-full"}),(0,r.jsx)(i.P.div,{animate:{x:[10,-10,10],y:[5,-5,5]},transition:{duration:5,repeat:1/0,ease:"easeInOut"},className:"absolute bottom-4 left-4 w-6 h-6 bg-white/30 rounded-full"})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold font-display text-black mb-3 group-hover:text-gray-700 transition-colors duration-200",children:e.title}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map(e=>(0,r.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full font-medium",children:e},e))}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-gray-500 group-hover:text-black transition-colors duration-200",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"View Project"}),(0,r.jsx)(i.P.svg,{className:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{x:0},whileHover:{x:5},transition:{duration:.2},children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]})]})})},e.title))})]})})}},4626:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(687),i=n(9877),a=n(5814),s=n.n(a);let o=()=>{let e={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},t={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},n=[{name:"Twitter",href:"https://x.com/Dmytri_Design",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})})},{name:"Dribbble",href:"https://dribbble.com/dmytriDesign",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 0C5.374 0 0 5.374 0 12s5.374 12 12 12 12-5.374 12-12S18.626 0 12 0zm7.568 5.302c1.4 1.5 2.252 3.5 2.273 5.698-.653-.126-7.512-1.5-7.512-1.5s-.126-.653-.378-1.4c3.274-1.274 5.617-3.798 5.617-3.798zM12 2.179c2.274 0 4.348.779 6.045 2.071 0 0-2.217 2.398-5.364 3.547C11.26 5.746 9.742 3.798 9.742 3.798 10.456 2.905 11.188 2.179 12 2.179zm-2.71 2.71s1.518 1.948 2.962 4.023c-3.798 1.022-7.134 1.022-7.134 1.022-.378-2.146.252-4.171 1.4-5.794.653.252 1.778.504 2.772.749zm-4.171 7.386s3.798 0 7.89-1.274c.252.504.378 1.022.63 1.526-4.674 1.274-7.134 4.8-7.134 4.8-1.526-1.652-2.398-3.924-1.386-5.052zm2.024 6.171s2.146-3.274 6.423-4.548c1.022 2.65 1.4 4.8 1.526 5.542-1.4.63-3.022.882-4.674.882-1.148 0-2.146-.252-3.275-.876zm6.045-.504c-.126-.504-.378-2.524-1.274-4.926 2.146-.378 4.023.252 4.023.252-.378 2.02-1.4 3.798-2.749 4.674z"})})},{name:"LinkedIn",href:"https://www.linkedin.com/in/dmytri-design/",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})}];return(0,r.jsx)("footer",{className:"bg-black text-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)(i.P.div,{variants:e,initial:"hidden",whileInView:"visible",viewport:{once:!0},className:"py-16",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16",children:[(0,r.jsxs)(i.P.div,{variants:t,children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-white rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-black font-bold text-xl",children:"P"})}),(0,r.jsx)("span",{className:"ml-3 text-xl font-bold font-display",children:"Panda Patronage"})]}),(0,r.jsx)("p",{className:"text-gray-300 text-lg leading-relaxed max-w-md",children:"Get in touch to find out more about digital experiences to effectively reach and engage customers and target audiences."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-8",children:[(0,r.jsxs)(i.P.div,{variants:t,children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Company"}),(0,r.jsx)("ul",{className:"space-y-4",children:[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Contact",href:"/contact"},{name:"Cases",href:"/cases"},{name:"Article",href:"/article"}].map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(s(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]}),(0,r.jsxs)(i.P.div,{variants:t,children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Legal"}),(0,r.jsx)("ul",{className:"space-y-4",children:[{name:"Privacy Policy",href:"/legal/privacy-policy"},{name:"Licensing",href:"/legal/licensing"},{name:"Terms of Use",href:"/legal/terms-of-use"}].map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(s(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]})]})]})}),(0,r.jsx)(i.P.div,{variants:e,initial:"hidden",whileInView:"visible",viewport:{once:!0},className:"border-t border-gray-800 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0",children:[(0,r.jsx)(i.P.p,{variants:t,className:"text-gray-400 text-sm",children:"\xa9 Panda-Patronage 2025"}),(0,r.jsx)(i.P.div,{variants:t,className:"flex space-x-6",children:n.map(e=>(0,r.jsx)(i.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.2,y:-2},transition:{duration:.2},className:"text-gray-400 hover:text-white transition-colors duration-200",children:e.icon},e.name))})]})})]})})}},4642:(e,t)=>{"use strict";function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},4674:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let r=n(4949),i=n(1550),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:a}=(0,i.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4722:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return s}});let r=n(5531),i=n(5499);function a(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,n,r)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&n===r.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4743:(e,t,n)=>{Promise.resolve().then(n.bind(n,6266)),Promise.resolve().then(n.bind(n,6920)),Promise.resolve().then(n.bind(n,7182)),Promise.resolve().then(n.bind(n,221)),Promise.resolve().then(n.bind(n,1245)),Promise.resolve().then(n.bind(n,2175)),Promise.resolve().then(n.bind(n,2385)),Promise.resolve().then(n.bind(n,793))},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},4949:(e,t)=>{"use strict";function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},5007:(e,t,n)=>{Promise.resolve().then(n.bind(n,8604)),Promise.resolve().then(n.bind(n,4626)),Promise.resolve().then(n.bind(n,124)),Promise.resolve().then(n.bind(n,4183)),Promise.resolve().then(n.bind(n,4551)),Promise.resolve().then(n.bind(n,2285)),Promise.resolve().then(n.bind(n,3591)),Promise.resolve().then(n.bind(n,2899))},5076:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return s}});let r=n(5144),i=n(5334),a=new r.PromiseQueue(5),s=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5144:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let r=n(6312),i=n(9656);var a=i._("_maxConcurrency"),s=i._("_runningCount"),o=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,n,i=new Promise((e,r)=>{t=e,n=r}),a=async()=>{try{r._(this,s)[s]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,s)[s]--,r._(this,l)[l]()}};return r._(this,o)[o].push({promiseFn:i,task:a}),r._(this,l)[l](),i}bump(e){let t=r._(this,o)[o].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,o)[o].splice(t,1)[0];r._(this,o)[o].unshift(e),r._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),r._(this,a)[a]=e,r._(this,s)[s]=0,r._(this,o)[o]=[]}}function c(e){if(void 0===e&&(e=!1),(r._(this,s)[s]<r._(this,a)[a]||e)&&r._(this,o)[o].length>0){var t;null==(t=r._(this,o)[o].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5232:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return x},navigateReducer:function(){return function e(t,n){let{url:P,isExternalUrl:w,navigateType:j,shouldScroll:E,allowAliasing:R}=n,T={},{hash:_}=P,S=(0,i.createHrefFromUrl)(P),A="push"===j;if((0,g.prunePrefetchCache)(t.prefetchCache),T.preserveCustomHistoryState=!1,T.pendingPush=A,w)return x(t,T,P.toString(),A);if(document.getElementById("__next-page-redirect"))return x(t,T,S,A);let M=(0,g.getOrCreatePrefetchCacheEntry)({url:P,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:R}),{treeAtTimeOfPrefetch:C,data:O}=M;return h.prefetchQueue.bump(O),O.then(h=>{let{flightData:g,canonicalUrl:w,postponed:j}=h,R=Date.now(),O=!1;if(M.lastUsedTime||(M.lastUsedTime=R,O=!0),M.aliased){let r=(0,v.handleAliasedPrefetchEntry)(R,t,g,P,T);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof g)return x(t,T,g,A);let N=w?(0,i.createHrefFromUrl)(w):S;if(_&&t.canonicalUrl.split("#",1)[0]===N.split("#",1)[0])return T.onlyHashChange=!0,T.canonicalUrl=N,T.shouldScroll=E,T.hashFragment=_,T.scrollableSegments=[],(0,c.handleMutable)(t,T);let k=t.tree,D=t.cache,L=[];for(let e of g){let{pathToSegment:n,seedData:i,head:c,isHeadPartial:h,isRootRender:g}=e,v=e.tree,w=["",...n],E=(0,s.applyRouterStatePatchToTree)(w,k,v,S);if(null===E&&(E=(0,s.applyRouterStatePatchToTree)(w,C,v,S)),null!==E){if(i&&g&&j){let e=(0,m.startPPRNavigation)(R,D,k,v,i,c,h,!1,L);if(null!==e){if(null===e.route)return x(t,T,S,A);E=e.route;let n=e.node;null!==n&&(T.cache=n);let i=e.dynamicRequestTree;if(null!==i){let n=(0,r.fetchServerResponse)(P,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,n)}}else E=v}else{if((0,l.isNavigatingToNewRootLayout)(k,E))return x(t,T,S,A);let r=(0,f.createEmptyCacheNode)(),i=!1;for(let t of(M.status!==u.PrefetchCacheEntryStatus.stale||O?i=(0,d.applyFlightData)(R,D,r,e,M):(i=function(e,t,n,r){let i=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),b(r).map(e=>[...n,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,a),i=!0;return i}(r,D,n,v),M.lastUsedTime=R),(0,o.shouldHardNavigate)(w,k)?(r.rsc=D.rsc,r.prefetchRsc=D.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(r,D,n),T.cache=r):i&&(T.cache=r,D=r),b(v))){let e=[...n,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&L.push(e)}}k=E}}return T.patchedTree=k,T.canonicalUrl=N,T.scrollableSegments=L,T.hashFragment=_,T.shouldScroll=E,(0,c.handleMutable)(t,T)},()=>t)}}});let r=n(9008),i=n(7391),a=n(8468),s=n(6770),o=n(5951),l=n(2030),u=n(9154),c=n(9435),d=n(6928),h=n(5076),f=n(9752),p=n(3913),m=n(5956),g=n(5334),y=n(7464),v=n(9707);function x(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function b(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,i]of Object.entries(r))for(let r of b(i))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return h},STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let r=n(9008),i=n(9154),a=n(5076);function s(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function o(e,t,n){return s(e,t===i.PrefetchKind.FULL,n)}function l(e){let{url:t,nextUrl:n,tree:r,prefetchCache:a,kind:o,allowAliasing:l=!0}=e,u=function(e,t,n,r,a){for(let o of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[n,null])){let n=s(e,!0,o),l=s(e,!1,o),u=e.search?n:l,c=r.get(u);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=r.get(l);if(a&&e.search&&t!==i.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==i.PrefetchKind.FULL&&a){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,o,n,a,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&o===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:null!=o?o:i.PrefetchKind.TEMPORARY})}),o&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=o),u):c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:o||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:n,prefetchCache:r,url:a,data:s,kind:l}=e,u=s.couldBeIntercepted?o(a,l,t):o(a,l),c={treeAtTimeOfPrefetch:n,data:Promise.resolve(s),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:a};return r.set(u,c),c}function c(e){let{url:t,kind:n,tree:s,nextUrl:l,prefetchCache:u}=e,c=o(t,n),d=a.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:s,nextUrl:l,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:i}=e,a=r.get(i);if(!a)return;let s=o(t,a.kind,n);return r.set(s,{...a,key:s}),r.delete(i),s}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=n?n:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),h={treeAtTimeOfPrefetch:s,data:d,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,h),h}function d(e){for(let[t,n]of e)p(n)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let h=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:a}=e;return -1!==a?Date.now()<n+a?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+h?r?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<n+f?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<n+f?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:n,value:e[n++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===r){t.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===r){t.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===r){for(var i="",a=n+1;a<e.length;){var s=e.charCodeAt(a);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:i}),n=a;continue}if("("===r){var o=1,l="",a=n+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at "+n);if(!l)throw TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:l}),n=a;continue}t.push({type:"CHAR",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,a=void 0===r?"./":r,s="[^"+i(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<n.length&&n[u].type===e)return n[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var r=n[u];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},f=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<n.length;){var p=d("CHAR"),m=d("NAME"),g=d("PATTERN");if(m||g){var y=p||"";-1===a.indexOf(y)&&(c+=y,y=""),c&&(o.push(c),c=""),o.push({name:m||l++,prefix:y,suffix:"",pattern:g||s,modifier:d("MODIFIER")||""});continue}var v=p||d("ESCAPED_CHAR");if(v){c+=v;continue}if(c&&(o.push(c),c=""),d("OPEN")){var y=f(),x=d("NAME")||"",b=d("PATTERN")||"",P=f();h("CLOSE"),o.push({name:x||(b?l++:""),pattern:x&&!b?s:b,prefix:y,suffix:P,modifier:d("MODIFIER")||""});continue}h("END")}return o}function n(e,t){void 0===t&&(t={});var n=a(t),r=t.encode,i=void 0===r?function(e){return e}:r,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",n)});return function(t){for(var n="",r=0;r<e.length;r++){var a=e[r];if("string"==typeof a){n+=a;continue}var s=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,c="*"===a.modifier||"+"===a.modifier;if(Array.isArray(s)){if(!c)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===s.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<s.length;d++){var h=i(s[d],a);if(o&&!l[r].test(h))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+h+'"');n+=a.prefix+h+a.suffix}continue}if("string"==typeof s||"number"==typeof s){var h=i(String(s),a);if(o&&!l[r].test(h))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+h+'"');n+=a.prefix+h+a.suffix;continue}if(!u){var f=c?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+f)}}return n}}function r(e,t,n){void 0===n&&(n={});var r=n.decode,i=void 0===r?function(e){return e}:r;return function(n){var r=e.exec(n);if(!r)return!1;for(var a=r[0],s=r.index,o=Object.create(null),l=1;l<r.length;l++)!function(e){if(void 0!==r[e]){var n=t[e-1];"*"===n.modifier||"+"===n.modifier?o[n.name]=r[e].split(n.prefix+n.suffix).map(function(e){return i(e,n)}):o[n.name]=i(r[e],n)}}(l);return{path:a,index:s,params:o}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function s(e,t,n){void 0===n&&(n={});for(var r=n.strict,s=void 0!==r&&r,o=n.start,l=n.end,u=n.encode,c=void 0===u?function(e){return e}:u,d="["+i(n.endsWith||"")+"]|$",h="["+i(n.delimiter||"/#?")+"]",f=void 0===o||o?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)f+=i(c(m));else{var g=i(c(m.prefix)),y=i(c(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";f+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+v}else f+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else f+="("+m.pattern+")"+m.modifier;else f+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)s||(f+=h+"?"),f+=n.endsWith?"(?="+d+")":"$";else{var x=e[e.length-1],b="string"==typeof x?h.indexOf(x[x.length-1])>-1:void 0===x;s||(f+="(?:"+h+"(?="+d+"))?"),b||(f+="(?="+h+"|"+d+")")}return new RegExp(f,a(n))}function o(t,n,r){if(t instanceof RegExp){if(!n)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)n.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,n,r).source}).join("|")+")",a(r)):s(e(t,r),n,r)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,r){return n(e(t,r),r)},t.tokensToFunction=n,t.match=function(e,t){var n=[];return r(o(e,n,t),n,t)},t.regexpToFunction=r,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},5416:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return l},isBot:function(){return o}});let r=n(5796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=r.HTML_LIMITED_BOT_UA_RE.source;function s(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function o(e){return i.test(e)||s(e)}function l(e){return i.test(e)?"dom":s(e)?"html":void 0}},5526:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return h}});let r=n(5362),i=n(3293),a=n(6759),s=n(1437),o=n(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,n,r){void 0===n&&(n=[]),void 0===r&&(r=[]);let i={},a=n=>{let r,a=n.key;switch(n.type){case"header":a=a.toLowerCase(),r=e.headers[a];break;case"cookie":r="cookies"in e?e.cookies[n.key]:(0,o.getCookieParser)(e.headers)()[n.key];break;case"query":r=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};r=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!n.value&&r)return i[function(e){let t="";for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);(r>64&&r<91||r>96&&r<123)&&(t+=e[n])}return t}(a)]=r,!0;if(r){let e=RegExp("^"+n.value+"$"),t=Array.isArray(r)?r.slice(-1)[0].match(e):r.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===n.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!n.every(e=>a(e))||r.some(e=>a(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let n of Object.keys(t))e.includes(":"+n)&&(e=e.replace(RegExp(":"+n+"\\*","g"),":"+n+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+n+"\\?","g"),":"+n+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+n+"\\+","g"),":"+n+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+n+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+n));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let n of Object.keys({...e.params,...e.query}))n&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(n),"g"),"__ESC_COLON_"+n));let n=(0,a.parseUrl)(t),r=n.pathname;r&&(r=l(r));let s=n.href;s&&(s=l(s));let o=n.hostname;o&&(o=l(o));let u=n.hash;return u&&(u=l(u)),{...n,pathname:r,hostname:o,href:s,hash:u}}function h(e){let t,n,i=Object.assign({},e.query),a=d(e),{hostname:o,query:u}=a,h=a.pathname;a.hash&&(h=""+h+a.hash);let f=[],p=[];for(let e of((0,r.pathToRegexp)(h,p),p))f.push(e.name);if(o){let e=[];for(let t of((0,r.pathToRegexp)(o,e),e))f.push(t.name)}let m=(0,r.compile)(h,{validate:!1});for(let[n,i]of(o&&(t=(0,r.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[n]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(u[n]=c(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>f.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let n=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(n){"(..)(..)"===n?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=n;break}}try{let[r,i]=(n=m(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=r,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:n,destQuery:u,parsedDestination:a}}},5531:(e,t)=>{"use strict";function n(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return n}})},5796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},5814:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let r=n(740),i=n(687),a=r._(n(3210)),s=n(195),o=n(2142),l=n(9154),u=n(3038),c=n(9289),d=n(6127);n(148);let h=n(3406),f=n(1794),p=n(3690);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let t,n,r,[s,g]=(0,a.useOptimistic)(h.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:x,as:b,children:P,prefetch:w=null,passHref:j,replace:E,shallow:R,scroll:T,onClick:_,onMouseEnter:S,onTouchStart:A,legacyBehavior:M=!1,onNavigate:C,ref:O,unstable_dynamicOnHover:N,...k}=e;t=P,M&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let D=a.default.useContext(o.AppRouterContext),L=!1!==w,I=null===w?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:U,as:V}=a.default.useMemo(()=>{let e=m(x);return{href:e,as:b?m(b):e}},[x,b]);M&&(n=a.default.Children.only(t));let F=M?n&&"object"==typeof n&&n.ref:O,B=a.default.useCallback(e=>(null!==D&&(v.current=(0,h.mountLinkInstance)(e,U,D,I,L,g)),()=>{v.current&&((0,h.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,h.unmountPrefetchableInstance)(e)}),[L,U,D,I,g]),H={ref:(0,u.useMergedRef)(B,F),onClick(e){M||"function"!=typeof _||_(e),M&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),D&&(e.defaultPrevented||function(e,t,n,r,i,s,o){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(n||t,i?"replace":"push",null==s||s,r.current)})}}(e,U,V,v,E,T,C))},onMouseEnter(e){M||"function"!=typeof S||S(e),M&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),D&&L&&(0,h.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){M||"function"!=typeof A||A(e),M&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),D&&L&&(0,h.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,c.isAbsoluteUrl)(V)?H.href=V:M&&!j&&("a"!==n.type||"href"in n.props)||(H.href=(0,d.addBasePath)(V)),r=M?a.default.cloneElement(n,H):(0,i.jsx)("a",{...k,...H,children:t}),(0,i.jsx)(y.Provider,{value:s,children:r})}n(2708);let y=(0,a.createContext)(h.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5942:(e,t,n)=>{"use strict";function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(6736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5951:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[a,s]=n,[o,l]=t;return(0,i.matchSegment)(o,a)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),s[l]):!!Array.isArray(o)}}});let r=n(4007),i=n(4077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],i=t.parallelRoutes,s=new Map(i);for(let t in r){let n=r[t],o=n[0],l=(0,a.createRouterCacheKey)(o),u=i.get(t);if(void 0!==u){let r=u.get(l);if(void 0!==r){let i=e(r,n),a=new Map(u);a.set(l,i),s.set(t,a)}}}let o=t.rsc,l=y(o)&&"pending"===o.status;return{lazyData:null,rsc:o,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:s,navigatedAt:t.navigatedAt}}}});let r=n(3913),i=n(4077),a=n(3123),s=n(2030),o=n(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,n,s,o,u,h,f,p){return function e(t,n,s,o,u,h,f,p,m,g,y){let v=s[1],x=o[1],b=null!==h?h[2]:null;u||!0===o[4]&&(u=!0);let P=n.parallelRoutes,w=new Map(P),j={},E=null,R=!1,T={};for(let n in x){let s,o=x[n],d=v[n],h=P.get(n),_=null!==b?b[n]:null,S=o[0],A=g.concat([n,S]),M=(0,a.createRouterCacheKey)(S),C=void 0!==d?d[0]:void 0,O=void 0!==h?h.get(M):void 0;if(null!==(s=S===r.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:c(t,d,o,O,u,void 0!==_?_:null,f,p,A,y):m&&0===Object.keys(o[1]).length?c(t,d,o,O,u,void 0!==_?_:null,f,p,A,y):void 0!==d&&void 0!==C&&(0,i.matchSegment)(S,C)&&void 0!==O&&void 0!==d?e(t,O,d,o,u,_,f,p,m,A,y):c(t,d,o,O,u,void 0!==_?_:null,f,p,A,y))){if(null===s.route)return l;null===E&&(E=new Map),E.set(n,s);let e=s.node;if(null!==e){let t=new Map(h);t.set(M,e),w.set(n,t)}let t=s.route;j[n]=t;let r=s.dynamicRequestTree;null!==r?(R=!0,T[n]=r):T[n]=t}else j[n]=o,T[n]=o}if(null===E)return null;let _={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:w,navigatedAt:t};return{route:d(o,j),node:_,dynamicRequestTree:R?d(o,T):null,children:E}}(e,t,n,s,!1,o,u,h,f,[],p)}function c(e,t,n,r,i,u,c,f,p,m){return!i&&(void 0===t||(0,s.isNavigatingToNewRootLayout)(t,n))?l:function e(t,n,r,i,s,l,u,c){let f,p,m,g,y=n[1],v=0===Object.keys(y).length;if(void 0!==r&&r.navigatedAt+o.DYNAMIC_STALETIME_MS>t)f=r.rsc,p=r.loading,m=r.head,g=r.navigatedAt;else if(null===i)return h(t,n,null,s,l,u,c);else if(f=i[1],p=i[3],m=v?s:null,g=t,i[4]||l&&v)return h(t,n,i,s,l,u,c);let x=null!==i?i[2]:null,b=new Map,P=void 0!==r?r.parallelRoutes:null,w=new Map(P),j={},E=!1;if(v)c.push(u);else for(let n in y){let r=y[n],i=null!==x?x[n]:null,o=null!==P?P.get(n):void 0,d=r[0],h=u.concat([n,d]),f=(0,a.createRouterCacheKey)(d),p=e(t,r,void 0!==o?o.get(f):void 0,i,s,l,h,c);b.set(n,p);let m=p.dynamicRequestTree;null!==m?(E=!0,j[n]=m):j[n]=r;let g=p.node;if(null!==g){let e=new Map;e.set(f,g),w.set(n,e)}}return{route:n,node:{lazyData:null,rsc:f,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:w,navigatedAt:g},dynamicRequestTree:E?d(n,j):null,children:b}}(e,n,r,u,c,f,p,m)}function d(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function h(e,t,n,r,i,s,o){let l=d(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,n,r,i,s,o,l){let u=n[1],c=null!==r?r[2]:null,d=new Map;for(let n in u){let r=u[n],h=null!==c?c[n]:null,f=r[0],p=o.concat([n,f]),m=(0,a.createRouterCacheKey)(f),g=e(t,r,void 0===h?null:h,i,s,p,l),y=new Map;y.set(m,g),d.set(n,y)}let h=0===d.size;h&&l.push(o);let f=null!==r?r[1]:null,p=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==f?f:null,prefetchHead:h?i:[null,null],loading:void 0!==p?p:null,rsc:v(),head:h?v():null,navigatedAt:t}}(e,t,n,r,i,s,o),dynamicRequestTree:l,children:null}}function f(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:s,head:o}=t;s&&function(e,t,n,r,s){let o=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],a=o.children;if(null!==a){let e=a.get(n);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(r,t)){o=e;continue}}}return}!function e(t,n,r,s){if(null===t.dynamicRequestTree)return;let o=t.children,l=t.node;if(null===o){null!==l&&(function e(t,n,r,s,o){let l=n[1],u=r[1],c=s[2],d=t.parallelRoutes;for(let t in l){let n=l[t],r=u[t],s=c[t],h=d.get(t),f=n[0],p=(0,a.createRouterCacheKey)(f),g=void 0!==h?h.get(p):void 0;void 0!==g&&(void 0!==r&&(0,i.matchSegment)(f,r[0])&&null!=s?e(g,n,r,s,o):m(n,g,null))}let h=t.rsc,f=s[1];null===h?t.rsc=f:y(h)&&h.resolve(f);let p=t.head;y(p)&&p.resolve(o)}(l,t.route,n,r,s),t.dynamicRequestTree=null);return}let u=n[1],c=r[2];for(let t in n){let n=u[t],r=c[t],a=o.get(t);if(void 0!==a){let t=a.route[0];if((0,i.matchSegment)(n[0],t)&&null!=r)return e(a,n,r,s)}}}(o,n,r,s)}(e,n,r,s,o)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)m(e.route,n,t);else for(let e of r.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,n){let r=e[1],i=t.parallelRoutes;for(let e in r){let t=r[e],s=i.get(e);if(void 0===s)continue;let o=t[0],l=(0,a.createRouterCacheKey)(o),u=s.get(l);void 0!==u&&m(t,u,n)}let s=t.rsc;y(s)&&(null===n?s.resolve(null):s.reject(n));let o=t.head;y(o)&&o.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function v(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=g,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6010:()=>{},6044:(e,t,n)=>{"use strict";n.d(t,{xQ:()=>a});var r=n(3210),i=n(1279);function a(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:o}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return o(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},6127:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let r=n(8834),i=n(4674);function a(e,t){return(0,i.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6266:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\panda-patronage\\\\components\\\\FAQ.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\panda-patronage\\components\\FAQ.tsx","default")},6312:(e,t,n)=>{"use strict";function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},6341:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return f}});let r=n(9551),i=n(1959),a=n(2437),s=n(4396),o=n(8034),l=n(5526),u=n(2887),c=n(4722),d=n(6143),h=n(7912);function f(e,t,n){let i=(0,r.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let r=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),a=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(r||a||t.includes(e)||n&&Object.keys(n.groups).includes(e))&&delete i.query[e]}e.url=(0,r.format)(i)}function p(e,t,n){if(!n)return e;for(let r of Object.keys(n.groups)){let i,{optional:a,repeat:s}=n.groups[r],o=`[${s?"...":""}${r}]`;a&&(o=`[${o}]`);let l=t[r];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,i)}return e}function m(e,t,n,r){let i={};for(let a of Object.keys(t.groups)){let s=e[a];"string"==typeof s?s=(0,c.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(c.normalizeRscURL));let o=n[a],l=t.groups[a].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(o))||void 0===s&&!(l&&r))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${a}]]`))&&(s=void 0,delete e[a]),s&&"string"==typeof s&&t.groups[a].repeat&&(s=s.split("/")),s&&(i[a]=s)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:n,rewrites:r,pageIsDynamic:c,trailingSlash:d,caseSensitive:g}){let y,v,x;return c&&(y=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),x=(v=(0,o.getRouteMatcher)(y))(e)),{handleRewrites:function(s,o){let h={},f=o.pathname,p=r=>{let u=(0,a.getPathMatch)(r.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let p=u(o.pathname);if((r.has||r.missing)&&p){let e=(0,l.matchHas)(s,o.query,r.has,r.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:a,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:p,query:o.query});if(a.protocol)return!0;if(Object.assign(h,s,p),Object.assign(o.query,a.query),delete a.query,Object.assign(o,a),!(f=o.pathname))return!1;if(n&&(f=f.replace(RegExp(`^${n}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(f,t.locales);f=e.pathname,o.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(f===e)return!0;if(c&&v){let e=v(f);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of r.beforeFiles||[])p(e);if(f!==e){let t=!1;for(let e of r.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(f||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of r.fallback||[])if(t=p(e))break}}return h},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:n}=y,r=(0,o.getRouteMatcher)({re:{exec:e=>{let r=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(r)){let n=(0,h.normalizeNextQueryParam)(e);n&&(r[n]=t,delete r[e])}let i={};for(let e of Object.keys(n)){let a=n[e];if(!a)continue;let s=t[a],o=r[e];if(!s.optional&&!o)return null;i[s.pos]=o}return i}},groups:t})(e);return r||null},normalizeDynamicRouteParams:(e,t)=>y&&x?m(e,y,x,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>f(e,t,y),interpolateDynamicPath:(e,t)=>p(e,t,y)}}function y(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6361:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let r=n(6127);function i(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,n){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(r),s=(n||{}).decode||e,o=0;o<a.length;o++){var l=a[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,s))}}return i},t.serialize=function(e,t,r){var a=r||{},s=a.encode||n;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,n=encodeURIComponent,r=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6493:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let r=n(5232);function i(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6715:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},6736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let r=n(2255);function i(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let r=n(2785),i=n(3736);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,r.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6770:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,l){let u,[c,d,h,f,p]=n;if(1===t.length){let e=o(n,r);return(0,s.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,a.matchSegment)(m,c))return null;if(2===t.length)u=o(d[g],r);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),d[g],r,l)))return null;let y=[t[0],{...d,[g]:u},h,f];return p&&(y[4]=!0),(0,s.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let r=n(3913),i=n(4007),a=n(4077),s=n(2308);function o(e,t){let[n,i]=e,[s,l]=t;if(s===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(n,s)){let t={};for(let e in i)void 0!==l[e]?t[e]=o(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6920:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\panda-patronage\\\\components\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\panda-patronage\\components\\Footer.tsx","default")},6928:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let r=n(1500),i=n(3898);function a(e,t,n,a,s){let{tree:o,seedData:l,head:u,isRootRender:c}=a;if(null===l)return!1;if(c){let i=l[1];n.loading=l[3],n.rsc=i,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,o,l,u,s)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,n,t,a,s);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7022:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return s}});let r=n(3210),i=n(1215),a="next-route-announcer";function s(e){let{tree:t}=e,[n,s]=(0,r.useState)(null);(0,r.useEffect)(()=>(s(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[o,l]=(0,r.useState)(""),u=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),n?(0,i.createPortal)(o,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,n)=>{"use strict";n.d(t,{B:()=>r});let r="undefined"!=typeof window},7182:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\panda-patronage\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\panda-patronage\\components\\Header.tsx","default")},7464:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let s=a.length<=2,[o,l]=a,u=(0,i.createRouterCacheKey)(l),c=n.parallelRoutes.get(o),d=t.parallelRoutes.get(o);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(o,d));let h=null==c?void 0:c.get(u),f=d.get(u);if(s){f&&f.lazyData&&f!==h||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!f||!h){f||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},d.set(u,f)),e(f,h,(0,r.getNextFlightSegmentPath)(a))}}});let r=n(4007),i=n(3123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7810:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return A}});let r=n(1264),i=n(1448),a=n(1563),s=n(9154),o=n(6361),l=n(7391),u=n(5232),c=n(6770),d=n(2030),h=n(9435),f=n(1500),p=n(9752),m=n(8214),g=n(6493),y=n(2308),v=n(4007),x=n(6875),b=n(7860),P=n(5334),w=n(5942),j=n(6736),E=n(4642);n(593);let{createFromFetch:R,createTemporaryReferenceSet:T,encodeReply:_}=n(9357);async function S(e,t,n){let s,l,{actionId:u,actionArgs:c}=n,d=T(),h=(0,E.extractInfoFromServerReferenceId)(u),f="use-cache"===h.type?(0,E.omitUnusedArgs)(c,h):c,p=await _(f,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:u,[a.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[a.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,x]=(null==g?void 0:g.split(";"))||[];switch(x){case"push":s=b.RedirectType.push;break;case"replace":s=b.RedirectType.replace;break;default:s=void 0}let P=!!m.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let w=y?(0,o.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,j=m.headers.get("content-type");if(null==j?void 0:j.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await R(Promise.resolve(m),{callServer:r.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:d});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:w,redirectType:s,revalidatedParts:l,isPrerender:P}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:w,redirectType:s,revalidatedParts:l,isPrerender:P}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===j?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:w,redirectType:s,revalidatedParts:l,isPrerender:P}}function A(e,t){let{resolve:n,reject:r}=t,i={},a=e.tree;i.preserveCustomHistoryState=!1;let o=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return S(e,o,t).then(async m=>{let E,{actionResult:R,actionFlightData:T,redirectLocation:_,redirectType:S,isPrerender:A,revalidatedParts:M}=m;if(_&&(S===b.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=E=(0,l.createHrefFromUrl)(_,!1)),!T)return(n(R),_)?(0,u.handleExternalUrl)(e,i,_.href,e.pushRef.pendingPush):e;if("string"==typeof T)return n(R),(0,u.handleExternalUrl)(e,i,T,e.pushRef.pendingPush);let C=M.paths.length>0||M.tag||M.cookie;for(let r of T){let{tree:s,seedData:l,head:h,isRootRender:m}=r;if(!m)return console.log("SERVER ACTION APPLY FAILED"),n(R),e;let x=(0,c.applyRouterStatePatchToTree)([""],a,s,E||e.canonicalUrl);if(null===x)return n(R),(0,g.handleSegmentMismatch)(e,t,s);if((0,d.isNavigatingToNewRootLayout)(a,x))return n(R),(0,u.handleExternalUrl)(e,i,E||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],n=(0,p.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(v,n,void 0,s,l,h,void 0),i.cache=n,i.prefetchCache=new Map,C&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:x,updatedCache:n,includeNextUrl:!!o,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=x,a=x}return _&&E?(C||((0,P.createSeededPrefetchCacheEntry)({url:_,data:{flightData:T,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:A?s.PrefetchKind.FULL:s.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),r((0,x.getRedirectError)((0,j.hasBasePath)(E)?(0,w.removeBasePath)(E):E,S||b.RedirectType.push))):n(R),(0,h.handleMutable)(e,i)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7936:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(9008),n(7391),n(6770),n(2030),n(5232),n(9435),n(6928),n(9752),n(6493),n(8214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8034:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let r=n(4827);function i(e){let{re:t,groups:n}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(n)){let n=i[t.pos];void 0!==n&&(t.repeat?s[e]=n.split("/").map(e=>a(e)):s[e]=a(n))}return s}}},8035:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6346,23)),Promise.resolve().then(n.t.bind(n,7924,23)),Promise.resolve().then(n.t.bind(n,5656,23)),Promise.resolve().then(n.t.bind(n,99,23)),Promise.resolve().then(n.t.bind(n,8243,23)),Promise.resolve().then(n.t.bind(n,8827,23)),Promise.resolve().then(n.t.bind(n,2763,23)),Promise.resolve().then(n.t.bind(n,7173,23))},8171:(e,t,n)=>{"use strict";n.d(t,{s:()=>i});var r=n(4479);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},8212:(e,t,n)=>{"use strict";function r(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:r}=n(6415);return r(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return r}})},8265:(e,t,n)=>{"use strict";n.d(t,{W:()=>s});var r=n(3210),i=n(9292);let a={some:0,all:1};function s(e,{root:t,margin:n,amount:o,once:l=!1,initial:u=!1}={}){let[c,d]=(0,r.useState)(u);return(0,r.useEffect)(()=>{if(!e.current||l&&c)return;let r={root:t&&t.current||void 0,margin:n,amount:o};return function(e,t,{root:n,margin:r,amount:s="some"}={}){let o=(0,i.K)(e),l=new WeakMap,u=new IntersectionObserver(e=>{e.forEach(e=>{let n=l.get(e.target);if(!!n!==e.isIntersecting)if(e.isIntersecting){let n=t(e.target,e);"function"==typeof n?l.set(e.target,n):u.unobserve(e.target)}else"function"==typeof n&&(n(e),l.delete(e.target))})},{root:n,rootMargin:r,threshold:"number"==typeof s?s:a[s]});return o.forEach(e=>u.observe(e)),()=>u.disconnect()}(e.current,()=>(d(!0),l?void 0:()=>d(!1)),r)},[t,e,n,l,o]),c}},8304:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let r=n(2958),i=n(4722),a=n(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,n){let i=(n?"":"?")+"$",a=`\\d?${n?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${s.icon.filename}${a}${l(s.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${s.apple.filename}${a}${l(s.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${s.openGraph.filename}${a}${l(s.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${s.twitter.filename}${a}${l(s.twitter.extensions,t)}${i}`)],u=(0,r.normalizePathSep)(e);return o.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,a.isAppRouteRoute)(e)&&u(e,[],!1)}function h(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&u(t,[],!1)}},8468:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let s=a.length<=2,[o,l]=a,u=(0,r.createRouterCacheKey)(l),c=n.parallelRoutes.get(o);if(!c)return;let d=t.parallelRoutes.get(o);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(o,d)),s)return void d.delete(u);let h=c.get(u),f=d.get(u);f&&h&&(f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},d.set(u,f)),e(f,h,(0,i.getNextFlightSegmentPath)(a)))}}});let r=n(3123),i=n(4007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8604:(e,t,n)=>{"use strict";n.d(t,{default:()=>P});var r=n(687),i=n(9877),a=n(3210),s=n(2157),o=n(2789),l=n(2743),u=n(1279),c=n(8171),d=n(2582);class h extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f({children:e,isPresent:t,anchorX:n}){let i=(0,a.useId)(),s=(0,a.useRef)(null),o=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,a.useContext)(d.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:r,top:a,left:u,right:c}=o.current;if(t||!s.current||!e||!r)return;let d="left"===n?`left: ${u}`:`right: ${c}`;s.current.dataset.motionPopId=i;let h=document.createElement("style");return l&&(h.nonce=l),document.head.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${d}px !important;
            top: ${a}px !important;
          }
        `),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[t]),(0,r.jsx)(h,{isPresent:t,childRef:s,sizeRef:o,children:a.cloneElement(e,{ref:s})})}let p=({children:e,initial:t,isPresent:n,onExitComplete:i,custom:s,presenceAffectsLayout:l,mode:c,anchorX:d})=>{let h=(0,o.M)(m),p=(0,a.useId)(),g=!0,y=(0,a.useMemo)(()=>(g=!1,{id:p,initial:t,isPresent:n,custom:s,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;i&&i()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[n,h,i]);return l&&g&&(y={...y}),(0,a.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[n]),a.useEffect(()=>{n||h.size||!i||i()},[n]),"popLayout"===c&&(e=(0,r.jsx)(f,{isPresent:n,anchorX:d,children:e})),(0,r.jsx)(u.t.Provider,{value:y,children:e})};function m(){return new Map}var g=n(6044);let y=e=>e.key||"";function v(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let x=({children:e,custom:t,initial:n=!0,onExitComplete:i,presenceAffectsLayout:u=!0,mode:c="sync",propagate:d=!1,anchorX:h="left"})=>{let[f,m]=(0,g.xQ)(d),x=(0,a.useMemo)(()=>v(e),[e]),b=d&&!f?[]:x.map(y),P=(0,a.useRef)(!0),w=(0,a.useRef)(x),j=(0,o.M)(()=>new Map),[E,R]=(0,a.useState)(x),[T,_]=(0,a.useState)(x);(0,l.E)(()=>{P.current=!1,w.current=x;for(let e=0;e<T.length;e++){let t=y(T[e]);b.includes(t)?j.delete(t):!0!==j.get(t)&&j.set(t,!1)}},[T,b.length,b.join("-")]);let S=[];if(x!==E){let e=[...x];for(let t=0;t<T.length;t++){let n=T[t],r=y(n);b.includes(r)||(e.splice(t,0,n),S.push(n))}return"wait"===c&&S.length&&(e=S),_(v(e)),R(x),null}let{forceRender:A}=(0,a.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:T.map(e=>{let a=y(e),s=(!d||!!f)&&(x===T||b.includes(a));return(0,r.jsx)(p,{isPresent:s,initial:(!P.current||!!n)&&void 0,custom:t,presenceAffectsLayout:u,mode:c,onExitComplete:s?void 0:()=>{if(!j.has(a))return;j.set(a,!0);let e=!0;j.forEach(t=>{t||(e=!1)}),e&&(A?.(),_(w.current),d&&m?.(),i&&i())},anchorX:h,children:e},a)})})};var b=n(8265);let P=()=>{let e=(0,a.useRef)(null),t=(0,b.W)(e,{once:!0,margin:"-100px"}),[n,s]=(0,a.useState)(null),o={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},l={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},u=e=>{s(n===e?null:e)};return(0,r.jsx)("section",{ref:e,className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(i.P.div,{variants:o,initial:"hidden",animate:t?"visible":"hidden",className:"text-center mb-16",children:[(0,r.jsx)(i.P.h2,{variants:l,className:"text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mb-6",children:"Explore Our FAQs"}),(0,r.jsx)(i.P.p,{variants:l,className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Find quick answers to commonly asked questions about Neutra. Have a question not listed?"})]}),(0,r.jsx)(i.P.div,{variants:o,initial:"hidden",animate:t?"visible":"hidden",className:"space-y-4",children:[{question:"Setup Process?",answer:"Initial setup is quick and user-friendly, allowing immediate use."},{question:"Subscription Costs?",answer:"Various pricing plans are available to suit different budget needs."},{question:"User Support?",answer:"24/7 customer support is available via email, chat, and phone."},{question:"Customization Options?",answer:"Fully customizable to match your brand&apos;s style and preferences."},{question:"Refund Policy?",answer:"Full refunds provided within 30 days if not satisfied."},{question:"Upgrade Options?",answer:"Easy upgrades available for additional features and capabilities."}].map((e,t)=>(0,r.jsxs)(i.P.div,{variants:l,className:"bg-gray-50 rounded-2xl overflow-hidden",children:[(0,r.jsxs)("button",{onClick:()=>u(t),className:"w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-100 transition-colors duration-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-black pr-4",children:e.question}),(0,r.jsx)(i.P.div,{animate:{rotate:180*(n===t)},transition:{duration:.3},className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),(0,r.jsx)(x,{children:n===t&&(0,r.jsx)(i.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3,ease:"easeInOut"},className:"overflow-hidden",children:(0,r.jsx)("div",{className:"px-6 pb-6",children:(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.answer})})})})]},t))}),(0,r.jsxs)(i.P.div,{variants:l,initial:"hidden",animate:t?"visible":"hidden",className:"text-center mt-12",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Still have questions? We're here to help!"}),(0,r.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsxs)("a",{href:"/contact",className:"inline-flex items-center bg-black text-white px-6 py-3 rounded-full font-medium hover:bg-gray-800 transition-colors duration-200",children:["Contact Support",(0,r.jsx)("svg",{className:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})})]})]})})}},8627:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let r=n(7391),i=n(642);function a(e,t){var n;let{url:a,tree:s}=t,o=(0,r.createHrefFromUrl)(a),l=s||e.tree,u=e.cache;return{canonicalUrl:o,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(n=(0,i.extractPathFromFlightRouterState)(l))?n:a.pathname}}n(5956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(9154),n(5232),n(9651),n(8627),n(8866),n(5076),n(7936),n(7810);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8834:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let r=n(1550);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:i,hash:a}=(0,r.parsePath)(e);return""+t+n+i+a}},8866:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let r=n(9008),i=n(7391),a=n(6770),s=n(2030),o=n(5232),l=n(9435),u=n(1500),c=n(9752),d=n(6493),h=n(8214),f=n(2308);function p(e,t){let{origin:n}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,h.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,r.fetchServerResponse)(new URL(m,n),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let x=Date.now();return y.lazyData.then(async n=>{let{flightData:r,canonicalUrl:c}=n;if("string"==typeof r)return(0,o.handleExternalUrl)(e,p,r,e.pushRef.pendingPush);for(let n of(y.lazyData=null,r)){let{tree:r,seedData:l,head:h,isRootRender:b}=n;if(!b)return console.log("REFRESH FAILED"),e;let P=(0,a.applyRouterStatePatchToTree)([""],g,r,e.canonicalUrl);if(null===P)return(0,d.handleSegmentMismatch)(e,t,r);if((0,s.isNavigatingToNewRootLayout)(g,P))return(0,o.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let w=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=w),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(x,y,void 0,r,l,h,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({navigatedAt:x,state:e,updatedTree:P,updatedCache:y,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=P,g=P}return(0,l.handleMutable)(e,p)},()=>e)}n(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9058:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},9292:(e,t,n)=>{"use strict";function r(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let r=document;t&&(r=t.current);let i=n?.[e]??r.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}n.d(t,{K:()=>r})},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9435:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let r=n(642);function i(e){return void 0!==e}function a(e,t){var n,a;let s=null==(n=t.shouldScroll)||n,o=e.nextUrl;if(i(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?o=n:o||(o=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!s&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:s?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:s?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9551:e=>{"use strict";e.exports=require("url")},9651:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let r=n(7391),i=n(6770),a=n(2030),s=n(5232),o=n(6928),l=n(9435),u=n(9752);function c(e,t){let{serverResponse:{flightData:n,canonicalUrl:c},navigatedAt:d}=t,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof n)return(0,s.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of n){let{segmentPath:n,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...n],f,l,e.canonicalUrl);if(null===m)return e;if((0,a.isNavigatingToNewRootLayout)(f,m))return(0,s.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,r.createHrefFromUrl)(c):void 0;g&&(h.canonicalUrl=g);let y=(0,u.createEmptyCacheNode)();(0,o.applyFlightData)(d,p,y,t),h.patchedTree=m,h.cache=y,p=y,f=m}return(0,l.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9656:(e,t,n)=>{"use strict";n.r(t),n.d(t,{_:()=>i});var r=0;function i(e){return"__private_"+r+++"_"+e}},9707:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let r=n(3913),i=n(9752),a=n(6770),s=n(7391),o=n(3123),l=n(3898),u=n(9435);function c(e,t,n,c,h){let f,p=t.tree,m=t.cache,g=(0,s.createHrefFromUrl)(c);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=d(n,Object.fromEntries(c.searchParams));let{seedData:s,isRootRender:u,pathToSegment:h}=t,y=["",...h];n=d(n,Object.fromEntries(c.searchParams));let v=(0,a.applyRouterStatePatchToTree)(y,p,n,g),x=(0,i.createEmptyCacheNode)();if(u&&s){let t=s[1];x.loading=s[3],x.rsc=t,function e(t,n,i,a,s){if(0!==Object.keys(a[1]).length)for(let l in a[1]){let u,c=a[1][l],d=c[0],h=(0,o.createRouterCacheKey)(d),f=null!==s&&void 0!==s[2][l]?s[2][l]:null;if(null!==f){let e=f[1],n=f[3];u={lazyData:null,rsc:d.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=n.parallelRoutes.get(l);p?p.set(h,u):n.parallelRoutes.set(l,new Map([[h,u]])),e(t,u,i,c,f)}}(e,x,m,n,s)}else x.rsc=m.rsc,x.prefetchRsc=m.prefetchRsc,x.loading=m.loading,x.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,x,m,t);v&&(p=v,m=x,f=!0)}return!!f&&(h.patchedTree=p,h.cache=m,h.canonicalUrl=g,h.hashFragment=c.hash,(0,u.handleMutable)(t,h))}function d(e,t){let[n,i,...a]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),i,...a];let s={};for(let[e,n]of Object.entries(i))s[e]=d(n,t);return[n,s,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9752:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return A},createPrefetchURL:function(){return _},default:function(){return N},isExternalURL:function(){return T}});let r=n(740),i=n(687),a=r._(n(3210)),s=n(2142),o=n(9154),l=n(7391),u=n(449),c=n(9129),d=r._(n(5656)),h=n(5416),f=n(6127),p=n(7022),m=n(7086),g=n(4397),y=n(9330),v=n(5942),x=n(6736),b=n(642),P=n(2776),w=n(3690),j=n(6875),E=n(7860);n(3406);let R={};function T(e){return e.origin!==window.location.origin}function _(e){let t;if((0,h.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return T(t)?null:t}function S(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,i={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(i,"",r)):window.history.replaceState(i,"",r)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function A(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function M(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function C(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,i=null!==r?r:n;return(0,a.useDeferredValue)(n,i)}function O(e){let t,{actionQueue:n,assetPrefix:r,globalError:l}=e,h=(0,c.useActionQueue)(n),{canonicalUrl:f}=h,{searchParams:P,pathname:T}=(0,a.useMemo)(()=>{let e=new URL(f,"http://n");return{searchParams:e.searchParams,pathname:(0,x.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[f]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(R.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,E.isRedirectError)(t)){e.preventDefault();let n=(0,j.getURLFromRedirectError)(t);(0,j.getRedirectTypeFromError)(t)===E.RedirectType.push?w.publicAppRouterInstance.push(n,{}):w.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:_}=h;if(_.mpaNavigation){if(R.pendingMpaPath!==f){let e=window.location;_.pendingPush?e.assign(f):e.replace(f),R.pendingMpaPath=f}(0,a.use)(y.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=M(t),i&&n(i)),e(t,r,i)},window.history.replaceState=function(e,r,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=M(e),i&&n(i)),t(e,r,i)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,w.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:A,tree:O,nextUrl:N,focusAndScrollRef:k}=h,D=(0,a.useMemo)(()=>(0,g.findHeadInCache)(A,O[1]),[A,O]),I=(0,a.useMemo)(()=>(0,b.getSelectedParams)(O),[O]),U=(0,a.useMemo)(()=>({parentTree:O,parentCacheNode:A,parentSegmentPath:null,url:f}),[O,A,f]),V=(0,a.useMemo)(()=>({tree:O,focusAndScrollRef:k,nextUrl:N}),[O,k,N]);if(null!==D){let[e,n]=D;t=(0,i.jsx)(C,{headCacheNode:e},n)}else t=null;let F=(0,i.jsxs)(m.RedirectBoundary,{children:[t,A.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:O})]});return F=(0,i.jsx)(d.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:F}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(S,{appRouterState:h}),(0,i.jsx)(L,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:I,children:(0,i.jsx)(u.PathnameContext.Provider,{value:T,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:P,children:(0,i.jsx)(s.GlobalLayoutRouterContext.Provider,{value:V,children:(0,i.jsx)(s.AppRouterContext.Provider,{value:w.publicAppRouterInstance,children:(0,i.jsx)(s.LayoutRouterContext.Provider,{value:U,children:F})})})})})})]})}function N(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:a}=e;return(0,P.useNavFailureHandler)(),(0,i.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,i.jsx)(O,{actionQueue:t,assetPrefix:a,globalError:[n,r]})})}let k=new Set,D=new Set;function L(){let[,e]=a.default.useState(0),t=k.size;return(0,a.useEffect)(()=>{let n=()=>e(e=>e+1);return D.add(n),t!==k.size&&n(),()=>{D.delete(n)}},[t,e]),[...k].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=k.size;return k.add(e),k.size!==t&&D.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9877:(e,t,n)=>{"use strict";let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function a(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function s(e,t,n,r){if("function"==typeof t){let[i,s]=a(r);t=t(void 0!==n?n:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,s]=a(r);t=t(void 0!==n?n:e.custom,i,s)}return t}function o(e,t,n){let r=e.getProps();return s(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>aS});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function f(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,s=d.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,a=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:(e,t=!1,a=!1)=>{let o=a&&i?n:r;return t&&s.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(o=e,i){a=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,n.clear(),i=!1,a&&(a=!1,c.process(e))}};return c}(a,t?n:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:g,postRender:y}=s,v=()=>{let a=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,40),1)),i.timestamp=a,i.isProcessing=!0,o.process(i),l.process(i),u.process(i),f.process(i),p.process(i),m.process(i),g.process(i),y.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(v))},x=()=>{n=!0,r=!0,i.isProcessing||e(v)};return{schedule:d.reduce((e,t)=>{let r=s[t];return e[t]=(e,t=!1,i=!1)=>(n||x(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)s[d[t]].cancel(e)},state:i,steps:s}}let{schedule:p,cancel:m,state:g,steps:y}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),b=new Set(["width","height","top","left","right","bottom",...v]);function P(e,t){-1===e.indexOf(t)&&e.push(t)}function w(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class j{constructor(){this.subscriptions=[]}add(e){return P(this.subscriptions,e),()=>w(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function E(){r=void 0}let R={now:()=>(void 0===r&&R.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(E)}},T=e=>!isNaN(parseFloat(e)),_={current:void 0};class S{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=R.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=R.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=T(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new j);let n=this.events[e].add(t);return"change"===e?()=>{n(),p.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return _.current&&_.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=R.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function A(e,t){return new S(e,t)}let M=e=>Array.isArray(e),C=e=>!!(e&&e.getVelocity);function O(e,t){let n=e.getValue("willChange");if(C(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let N=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),k="data-"+N("framerAppearId"),D=(e,t)=>n=>t(e(n)),L=(...e)=>e.reduce(D),I=(e,t,n)=>n>t?t:n<e?e:n,U=e=>1e3*e,V=e=>e/1e3,F={layout:0,mainThread:0,waapi:0},B=()=>{},H=()=>{},$=e=>t=>"string"==typeof t&&t.startsWith(e),z=$("--"),W=$("var(--"),K=e=>!!W(e)&&q.test(e.split("/*")[0].trim()),q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,X={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},G={...X,transform:e=>I(0,1,e)},Y={...X,default:1},Q=e=>Math.round(1e5*e)/1e5,Z=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,a,s,o]=r.match(Z);return{[e]:parseFloat(i),[t]:parseFloat(a),[n]:parseFloat(s),alpha:void 0!==o?parseFloat(o):1}},en=e=>I(0,255,e),er={...X,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Q(G.transform(r))+")"},ea={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eo=es("deg"),el=es("%"),eu=es("px"),ec=es("vh"),ed=es("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ef={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Q(t))+", "+el.transform(Q(n))+", "+Q(G.transform(r))+")"},ep={test:e=>ei.test(e)||ea.test(e)||ef.test(e),parse:e=>ei.test(e)?ei.parse(e):ef.test(e)?ef.parse(e):ea.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ef.transform(e)},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ey="color",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ex(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],a=0,s=t.replace(ev,e=>(ep.test(e)?(r.color.push(a),i.push(ey),n.push(ep.parse(e))):e.startsWith("var(")?(r.var.push(a),i.push("var"),n.push(e)):(r.number.push(a),i.push(eg),n.push(parseFloat(e))),++a,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function eb(e){return ex(e).values}function eP(e){let{split:t,types:n}=ex(e),r=t.length;return e=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],void 0!==e[a]){let t=n[a];t===eg?i+=Q(e[a]):t===ey?i+=ep.transform(e[a]):i+=e[a]}return i}}let ew=e=>"number"==typeof e?0:e,ej={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Z)?.length||0)+(e.match(em)?.length||0)>0},parse:eb,createTransformer:eP,getAnimatableNone:function(e){let t=eb(e);return eP(e)(t.map(ew))}};function eE(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eR(e,t){return n=>n>0?t:e}let eT=(e,t,n)=>e+(t-e)*n,e_=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eS=[ea,ei,ef],eA=e=>eS.find(t=>t.test(e));function eM(e){let t=eA(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ef&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,a=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,o=2*n-r;i=eE(o,r,e+1/3),a=eE(o,r,e),s=eE(o,r,e-1/3)}else i=a=s=n;return{red:Math.round(255*i),green:Math.round(255*a),blue:Math.round(255*s),alpha:r}}(n)),n}let eC=(e,t)=>{let n=eM(e),r=eM(t);if(!n||!r)return eR(e,t);let i={...n};return e=>(i.red=e_(n.red,r.red,e),i.green=e_(n.green,r.green,e),i.blue=e_(n.blue,r.blue,e),i.alpha=eT(n.alpha,r.alpha,e),ei.transform(i))},eO=new Set(["none","hidden"]);function eN(e,t){return n=>eT(e,t,n)}function ek(e){return"number"==typeof e?eN:"string"==typeof e?K(e)?eR:ep.test(e)?eC:eI:Array.isArray(e)?eD:"object"==typeof e?ep.test(e)?eC:eL:eR}function eD(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>ek(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eL(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=ek(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eI=(e,t)=>{let n=ej.createTransformer(t),r=ex(e),i=ex(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?eO.has(e)&&!i.values.length||eO.has(t)&&!r.values.length?function(e,t){return eO.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):L(eD(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let a=t.types[i],s=e.indexes[a][r[a]],o=e.values[s]??0;n[i]=o,r[a]++}return n}(r,i),i.values),n):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eR(e,t))};function eU(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eT(e,t,n):ek(e)(e,t)}let eV=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>g.isProcessing?g.timestamp:R.now()}},eF=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=e(t/(i-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function eB(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function eH(e,t,n){var r,i;let a=Math.max(t-5,0);return r=n-e(a),(i=t-a)?1e3/i*r:0}let e$={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ez(e,t){return e*Math.sqrt(1-t*t)}let eW=["duration","bounce"],eK=["stiffness","damping","mass"];function eq(e,t){return t.some(t=>void 0!==e[t])}function eX(e=e$.visualDuration,t=e$.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:a}=r,s=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:e$.velocity,stiffness:e$.stiffness,damping:e$.damping,mass:e$.mass,isResolvedFromDuration:!1,...e};if(!eq(e,eK)&&eq(e,eW))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*I(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:e$.mass,stiffness:r,damping:i}}else{let n=function({duration:e=e$.duration,bounce:t=e$.bounce,velocity:n=e$.velocity,mass:r=e$.mass}){let i,a;B(e<=U(e$.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=I(e$.minDamping,e$.maxDamping,s),e=I(e$.minDuration,e$.maxDuration,V(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/ez(t,s)*Math.exp(-i)},a=t=>{let r=t*s*e,a=Math.pow(s,2)*Math.pow(t,2)*e,o=Math.exp(-r),l=ez(Math.pow(t,2),s);return(r*n+n-a)*o*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),a=t=>e*e*(n-t)*Math.exp(-t*e));let o=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,a,5/e);if(e=U(e),isNaN(o))return{stiffness:e$.stiffness,damping:e$.damping,duration:e};{let t=Math.pow(o,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:e$.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-V(r.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*d)),y=o-s,v=V(Math.sqrt(u/d)),x=5>Math.abs(y);if(i||(i=x?e$.restSpeed.granular:e$.restSpeed.default),a||(a=x?e$.restDelta.granular:e$.restDelta.default),g<1){let e=ez(v,g);n=t=>o-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)n=e=>o-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*v*t),r=Math.min(e*t,300);return o-n*((m+g*v*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let b={calculatedDuration:p&&h||null,next:e=>{let t=n(e);if(p)l.done=e>=h;else{let r=0===e?m:0;g<1&&(r=0===e?U(m):eH(n,e,t));let s=Math.abs(o-t)<=a;l.done=Math.abs(r)<=i&&s}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eB(b),2e4),t=eF(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eG({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:s,min:o,max:l,restDelta:u=.5,restSpeed:c}){let d,h,f=e[0],p={done:!1,value:f},m=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,y=n*t,v=f+y,x=void 0===s?v:s(v);x!==v&&(y=x-f);let b=e=>-y*Math.exp(-e/r),P=e=>x+b(e),w=e=>{let t=b(e),n=P(e);p.done=Math.abs(t)<=u,p.value=p.done?x:n},j=e=>{m(p.value)&&(d=e,h=eX({keyframes:[p.value,g(p.value)],velocity:eH(P,e,p.value),damping:i,stiffness:a,restDelta:u,restSpeed:c}))};return j(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,w(e),j(e)),void 0!==d&&e>=d)?h.next(e-d):(t||w(e),p)}}}eX.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(eB(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:V(i)}}(e,100,eX);return e.ease=t.ease,e.duration=U(t.duration),e.type="keyframes",e};let eY=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eQ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let a,s,o=0;do(a=eY(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(a)>1e-7&&++o<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:eY(i(e),t,r)}let eZ=eQ(.42,0,1,1),eJ=eQ(0,0,.58,1),e0=eQ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e3=e=>t=>1-e(1-t),e4=eQ(.33,1.53,.69,.99),e5=e3(e4),e6=e2(e5),e7=e=>(e*=2)<1?.5*e5(e):.5*(2-Math.pow(2,-10*(e-1))),e9=e=>1-Math.sin(Math.acos(e)),e8=e3(e9),te=e2(e9),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eZ,easeInOut:e0,easeOut:eJ,circIn:e9,circInOut:te,circOut:e8,backIn:e5,backInOut:e6,backOut:e4,anticipate:e7},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){H(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eQ(t,n,r,i)}return tr(e)?(H(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},ta=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let a=e1(r)?r.map(ti):ti(r),s={done:!1,value:t[0]},o=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let a=e.length;if(H(a===t.length,"Both input and output ranges must be the same length"),1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,n){let r=[],i=n||c.mix||eU,a=e.length-1;for(let n=0;n<a;n++){let a=i(e[n],e[n+1]);t&&(a=L(Array.isArray(t)?t[n]||u:t,a)),r.push(a)}return r}(t,r,i),l=o.length,d=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=ta(e[r],e[r+1],n);return o[r](i)};return n?t=>d(I(e[0],e[a-1],t)):d}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=ta(0,t,r);e.push(eT(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(a)?a:t.map(()=>a||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}let to=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let a=e.filter(to),s=i<0||t&&"loop"!==n&&t%2==1?0:a.length-1;return s&&void 0!==r?r:a[s]}let tu={decay:eG,inertia:eG,tween:ts,keyframes:ts,spring:eX};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tf extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==R.now()&&this.tick(R.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},F.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ts,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:a=0}=e,{keyframes:s}=e,o=t||ts;o!==ts&&"number"!=typeof s[0]&&(this.mixKeyframes=L(th,eU(s[0],s[1])),s=[0,100]);let l=o({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=o({...e,keyframes:[...s].reverse(),velocity:-a})),null===l.calculatedDuration&&(l.calculatedDuration=eB(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:s,calculatedDuration:o}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,x=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/s)):"mirror"===d&&(x=a)),v=I(0,1,n)*s}let b=y?{done:!1,value:u[0]}:x.next(v);i&&(b.value=i(b.value));let{done:P}=b;y||null===o||(P=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return w&&f!==eG&&(b.value=tl(u,this.options,m,this.speed)),p&&p(b.value),w&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return V(this.calculatedDuration)}get time(){return V(this.currentTime)}set time(e){e=U(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(R.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=V(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eV,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(R.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,F.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>ty(tp(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},ty=e=>((e%=360)<0&&(e+=360),e),tv=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tx=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tv,scaleY:tx,scale:e=>(tv(e)+tx(e))/2,rotateX:e=>ty(tp(Math.atan2(e[6],e[5]))),rotateY:e=>ty(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tP(e){return+!!e.includes("scale")}function tw(e,t){let n,r;if(!e||"none"===e)return tP(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tb,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tg,r=t}if(!r)return tP(t);let a=n[t],s=r[1].split(",").map(tE);return"function"==typeof a?a(s):s[a]}let tj=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tw(n,t)};function tE(e){return parseFloat(e.trim())}let tR=e=>e===X||e===eu,tT=new Set(["x","y","z"]),t_=v.filter(e=>!tT.has(e)),tS={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tw(t,"x"),y:(e,{transform:t})=>tw(t,"y")};tS.translateX=tS.x,tS.translateY=tS.y;let tA=new Set,tM=!1,tC=!1,tO=!1;function tN(){if(tC){let e=Array.from(tA).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return t_.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tC=!1,tM=!1,tA.forEach(e=>e.complete(tO)),tA.clear()}function tk(){tA.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tC=!0)})}class tD{constructor(e,t,n,r,i,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(tA.add(this),tM||(tM=!0,p.read(tk),p.resolveKeyframes(tN))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),a=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,a);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=a),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tA.delete(this)}cancel(){"scheduled"===this.state&&(tA.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tL=e=>e.startsWith("--");function tI(e){let t;return()=>(void 0===t&&(t=e()),t)}let tU=tI(()=>void 0!==window.ScrollTimeline),tV={},tF=function(e,t){let n=tI(e);return()=>tV[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tB=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tB([0,.65,.55,1]),circOut:tB([.55,0,1,.45]),backIn:tB([.31,.01,.66,-.59]),backOut:tB([.33,1.53,.69,.99])};function t$(e){return"function"==typeof e&&"applyToOptions"in e}class tz extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:s,onComplete:o}=e;this.isPseudoElement=!!i,this.allowFlatten=a,this.options=e,H("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return t$(e)&&tF()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:a=0,repeatType:s="loop",ease:o="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?tF()?eF(t,n):"ease-out":tt(t)?tB(t):Array.isArray(t)?t.map(t=>e(t,n)||tH.easeOut):tH[t]}(o,i);Array.isArray(d)&&(c.easing=d),h.value&&F.waapi++;let f={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:"reverse"===s?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return h.value&&p.finished.finally(()=>{F.waapi--}),p}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tL(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return V(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return V(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=U(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tU())?(this.animation.timeline=e,u):t(this)}}let tW={anticipate:e7,backInOut:e6,circInOut:te};class tK extends tz{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tW&&(e.ease=tW[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...a}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tf({...a,autoplay:!1}),o=U(this.finishedTime??this.time);t.setWithVelocity(s.sample(o-10).value,s.sample(o).value,10),s.stop()}}let tq=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(ej.test(e)||"0"===e)&&!e.startsWith("url("));var tX,tG,tY=n(8171);let tQ=new Set(["opacity","clipPath","filter","transform"]),tZ=tI(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends td{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:a="loop",keyframes:s,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=R.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:a,name:o,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tD;this.keyframeResolver=new h(s,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:a,velocity:s,delay:o,isHandoff:l,onUpdate:d}=n;this.resolvedAt=R.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let a=e[e.length-1],s=tq(i,t),o=tq(a,t);return B(s===o,`You are trying to animate ${t} from "${i}" to "${a}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${a} via the \`style\` property.`),!!s&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||t$(n))&&r)}(e,i,a,s)&&((c.instantAnimations||!o)&&d?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},f=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:a,type:s}=e;if(!(0,tY.s)(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return tZ()&&n&&tQ.has(n)&&("transform"!==n||!l)&&!o&&!r&&"mirror"!==i&&0!==a&&"inertia"!==s}(h)?new tK({...h,element:h.motionValue.owner.current}):new tf(h);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tO=!0,tk(),tN(),tO=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t3={type:"keyframes",duration:.8},t4={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t5=(e,{keyframes:t})=>t.length>2?t3:x.has(e)?e.startsWith("scale")?t2(t[1]):t1:t4,t6=(e,t,n,r={},i,a)=>s=>{let o=l(r,e)||{},u=o.delay||r.delay||0,{elapsed:d=0}=r;d-=U(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-d,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{s(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:a?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:a,repeatType:s,repeatDelay:o,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)&&Object.assign(h,t5(e,h)),h.duration&&(h.duration=U(h.duration)),h.repeatDelay&&(h.repeatDelay=U(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let f=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(f=!0)),(c.instantAnimations||c.skipAnimations)&&(f=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,f&&!a&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),a=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[a]}(h.keyframes,o);if(void 0!==e)return void p.update(()=>{h.onUpdate(e),h.onComplete()})}return o.isSync?new tf(h):new tJ(h)};function t7(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:a=e.getDefaultTransition(),transitionEnd:s,...u}=t;r&&(a=r);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(d,t))continue;let s={delay:n,...l(a||{},t)},o=r.get();if(void 0!==o&&!r.isAnimating&&!Array.isArray(i)&&i===o&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let n=e.props[k];if(n){let e=window.MotionHandoffAnimation(n,t,p);null!==e&&(s.startTime=e,h=!0)}}O(e,t),r.start(t6(t,r,i,e.shouldReduceMotion&&b.has(t)?{type:!1}:s,e,h));let f=r.animation;f&&c.push(f)}return s&&Promise.all(c).then(()=>{p.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=o(e,t)||{};for(let t in i={...i,...n}){var a;let n=M(a=i[t])?a[a.length-1]||0:a;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,A(n))}}(e,s)})}),c}function t9(e,t,n={}){let r=o(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let a=r?()=>Promise.all(t7(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:a=0,staggerChildren:s,staggerDirection:o}=i;return function(e,t,n=0,r=0,i=1,a){let s=[],o=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>o-e*r;return Array.from(e.variantChildren).sort(t8).forEach((e,r)=>{e.notify("AnimationStart",t),s.push(t9(e,t,{...a,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,a+r,s,o,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([a(),s(n.delay)]);{let[e,t]="beforeChildren"===l?[a,s]:[s,a];return e().then(()=>t())}}function t8(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,na=[...nn].reverse(),ns=nn.length;function no(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:no(!0),whileInView:no(),whileHover:no(),whileTap:no(),whileDrag:no(),whileFocus:no(),exit:no()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t9(e,t,n)));else if("string"==typeof t)r=t9(e,t,n);else{let i="function"==typeof t?o(e,t,n.custom):t;r=Promise.all(t7(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,a=t=>(n,r)=>{let i=o(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],d=new Set,h={},f=1/0;for(let t=0;t<ns;t++){var p,m;let o=na[t],g=n[o],y=void 0!==l[o]?l[o]:u[o],v=nt(y),x=o===s?g.isActive:null;!1===x&&(f=t);let b=y===u[o]&&y!==l[o]&&v;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...h},!g.isActive&&null===x||!y&&!g.prevProp||i(y)||"boolean"==typeof y)continue;let P=(p=g.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!ne(m,p)),w=P||o===s&&g.isActive&&!b&&v||t>f&&v,j=!1,E=Array.isArray(y)?y:[y],R=E.reduce(a(o),{});!1===x&&(R={});let{prevResolvedValues:T={}}=g,_={...T,...R},S=t=>{w=!0,d.has(t)&&(j=!0,d.delete(t)),g.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in _){let t=R[e],n=T[e];if(h.hasOwnProperty(e))continue;let r=!1;(M(t)&&M(n)?ne(t,n):t===n)?void 0!==t&&d.has(e)?S(e):g.protectedKeys[e]=!0:null!=t?S(e):d.add(e)}g.prevProp=y,g.prevResolvedValues=R,g.isActive&&(h={...h,...R}),r&&e.blockInitialAnimation&&(w=!1);let A=!(b&&P)||j;w&&A&&c.push(...E.map(e=>({animation:e,options:{type:o}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let n=o(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}d.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let g=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=s(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nd=0;class nh extends nu{constructor(){super(...arguments),this.id=nd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let nf={x:!1,y:!1};function np(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ng(e){return{point:{x:e.pageX,y:e.pageY}}}let ny=e=>t=>nm(t)&&e(t,ng(t));function nv(e,t,n,r){return np(e,t,ny(n),r)}function nx({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nb(e){return e.max-e.min}function nP(e,t,n,r=.5){e.origin=r,e.originPoint=eT(t.min,t.max,e.origin),e.scale=nb(n)/nb(t),e.translate=eT(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nw(e,t,n,r){nP(e.x,t.x,n.x,r?r.originX:void 0),nP(e.y,t.y,n.y,r?r.originY:void 0)}function nj(e,t,n){e.min=n.min+t.min,e.max=e.min+nb(t)}function nE(e,t,n){e.min=t.min-n.min,e.max=e.min+nb(t)}function nR(e,t,n){nE(e.x,t.x,n.x),nE(e.y,t.y,n.y)}let nT=()=>({translate:0,scale:1,origin:0,originPoint:0}),n_=()=>({x:nT(),y:nT()}),nS=()=>({min:0,max:0}),nA=()=>({x:nS(),y:nS()});function nM(e){return[e("x"),e("y")]}function nC(e){return void 0===e||1===e}function nO({scale:e,scaleX:t,scaleY:n}){return!nC(e)||!nC(t)||!nC(n)}function nN(e){return nO(e)||nk(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nk(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nD(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nL(e,t=0,n=1,r,i){e.min=nD(e.min,t,n,r,i),e.max=nD(e.max,t,n,r,i)}function nI(e,{x:t,y:n}){nL(e.x,t.translate,t.scale,t.originPoint),nL(e.y,n.translate,n.scale,n.originPoint)}function nU(e,t){e.min=e.min+t,e.max=e.max+t}function nV(e,t,n,r,i=.5){let a=eT(e.min,e.max,i);nL(e,t,n,a,r)}function nF(e,t){nV(e.x,t.x,t.scaleX,t.scale,t.originX),nV(e.y,t.y,t.scaleY,t.scale,t.originY)}function nB(e,t){return nx(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nH=({current:e})=>e?e.ownerDocument.defaultView:null;function n$(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let nz=(e,t)=>Math.abs(e-t);class nW{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nX(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nz(e.x,t.x)**2+nz(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=g;this.history.push({...r,timestamp:i});let{onStart:a,onMove:s}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nK(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=nX("pointercancel"===e.type?this.lastMoveEventInfo:nK(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,a),r&&r(e,a)},!nm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let a=nK(ng(e),this.transformPagePoint),{point:s}=a,{timestamp:o}=g;this.history=[{...s,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,nX(a,this.history)),this.removeListeners=L(nv(this.contextWindow,"pointermove",this.handlePointerMove),nv(this.contextWindow,"pointerup",this.handlePointerUp),nv(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function nK(e,t){return t?{point:t(e.point)}:e}function nq(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nX({point:e},t){return{point:e,delta:nq(e,nG(t)),offset:nq(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nG(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>U(.1)));)n--;if(!r)return{x:0,y:0};let a=V(i.timestamp-r.timestamp);if(0===a)return{x:0,y:0};let s={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nG(e){return e[e.length-1]}function nY(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nQ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nZ(e,t,n){return{min:nJ(e,t),max:nJ(e,n)}}function nJ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nA(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nW(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ng(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(nf[e])return null;else return nf[e]=!0,()=>{nf[e]=!1};return nf.x||nf.y?null:(nf.x=nf.y=!0,()=>{nf.x=nf.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nM(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nb(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),O(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:a}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),a&&a(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nM(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:nH(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&p.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eT(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eT(n,e,r.max):Math.min(e,n)),e}(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&n$(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nY(e.x,n,i),y:nY(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nZ(e,"left","right"),y:nZ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nM(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!n$(t))return!1;let r=t.current;H(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let a=function(e,t,n){let r=nB(e,n),{scroll:i}=t;return i&&(nU(r.x,i.offset.x),nU(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:nQ(e.x,a.x),y:nQ(e.y,a.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nx(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:s}=this.getProps(),o=this.constraints||{};return Promise.all(nM(s=>{if(!n2(s,t,this.currentDirection))return;let l=o&&o[s]||{};a&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return O(this.visualElement,e),n.start(t6(e,n,0,t,this.visualElement,!1))}stopAnimation(){nM(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nM(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nM(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:a}=r.layout.layoutBox[t];i.set(e[t]-eT(n,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!n$(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nM(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nb(e),i=nb(t);return i>r?n=ta(t.min,t.max-r,e.min):r>i&&(n=ta(e.min,e.max-i,t.min)),I(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nM(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];n.set(eT(i,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=nv(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();n$(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),p.read(t);let i=np(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nM(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:a=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:a,dragMomentum:s}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n3 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n4=e=>(t,n)=>{e&&p.postRender(()=>e(t,n))};class n5 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nW(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nH(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n4(e),onStart:n4(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&p.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=nv(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n6=n(687);let{schedule:n7}=f(queueMicrotask,!1);var n9=n(3210),n8=n(6044),re=n(2157);let rt=(0,n9.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ra={};class rs extends n9.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in rl)ra[e]=rl[e],z(e)&&(ra[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:a}=n;return a&&(a.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||p.postRender(()=>{let e=a.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n7.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ro(e){let[t,n]=(0,n8.xQ)(),r=(0,n9.useContext)(re.L);return(0,n6.jsx)(rs,{...e,layoutGroup:r,switchLayoutGroup:(0,n9.useContext)(rt),isPresent:t,safeToRemove:n})}let rl={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=ej.parse(e);if(r.length>5)return e;let i=ej.createTransformer(e),a=+("number"!=typeof r[0]),s=n.x.scale*t.x,o=n.y.scale*t.y;r[0+a]/=s,r[1+a]/=o;let l=eT(s,o,.5);return"number"==typeof r[2+a]&&(r[2+a]/=l),"number"==typeof r[3+a]&&(r[3+a]/=l),i(r)}}};var ru=n(4479);function rc(e){return(0,ru.G)(e)&&"ownerSVGElement"in e}let rd=(e,t)=>e.depth-t.depth;class rh{constructor(){this.children=[],this.isDirty=!1}add(e){P(this.children,e),this.isDirty=!0}remove(e){w(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rd),this.isDirty=!1,this.children.forEach(e)}}function rf(e){return C(e)?e.get():e}let rp=["TopLeft","TopRight","BottomLeft","BottomRight"],rm=rp.length,rg=e=>"string"==typeof e?parseFloat(e):e,ry=e=>"number"==typeof e||eu.test(e);function rv(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rx=rP(0,.5,e8),rb=rP(.5,.95,u);function rP(e,t,n){return r=>r<e?0:r>t?1:n(ta(e,t,r))}function rw(e,t){e.min=t.min,e.max=t.max}function rj(e,t){rw(e.x,t.x),rw(e.y,t.y)}function rE(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rR(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rT(e,t,[n,r,i],a,s){!function(e,t=0,n=1,r=.5,i,a=e,s=e){if(el.test(t)&&(t=parseFloat(t),t=eT(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let o=eT(a.min,a.max,r);e===a&&(o-=t),e.min=rR(e.min,t,n,o,i),e.max=rR(e.max,t,n,o,i)}(e,t[n],t[r],t[i],t.scale,a,s)}let r_=["x","scaleX","originX"],rS=["y","scaleY","originY"];function rA(e,t,n,r){rT(e.x,t,r_,n?n.x:void 0,r?r.x:void 0),rT(e.y,t,rS,n?n.y:void 0,r?r.y:void 0)}function rM(e){return 0===e.translate&&1===e.scale}function rC(e){return rM(e.x)&&rM(e.y)}function rO(e,t){return e.min===t.min&&e.max===t.max}function rN(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rk(e,t){return rN(e.x,t.x)&&rN(e.y,t.y)}function rD(e){return nb(e.x)/nb(e.y)}function rL(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rI{constructor(){this.members=[]}add(e){P(this.members,e),e.scheduleRender()}remove(e){if(w(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rV=["","X","Y","Z"],rF={visibility:"hidden"},rB=0;function rH(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function r$({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rB++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(rU.nodes=rU.calculatedTargetDeltas=rU.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(rJ),this.nodes.forEach(r0),this.nodes.forEach(rq),h.addProjectionMetrics&&h.addProjectionMetrics(rU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rh)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new j),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rc(t)&&!(rc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=R.now(),r=({timestamp:i})=>{let a=i-n;a>=250&&(m(r),e(a-t))};return p.setup(r,!0),()=>m(r)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rZ))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let a=this.options.transition||i.getDefaultTransition()||r6,{onLayoutAnimationStart:s,onLayoutAnimationComplete:o}=i.getProps(),u=!this.targetLayout||!rk(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(a,"layout"),onPlay:s,onComplete:o};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rZ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[k];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",p,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rG);return}this.isUpdating||this.nodes.forEach(rY),this.isUpdating=!1,this.nodes.forEach(rQ),this.nodes.forEach(rz),this.nodes.forEach(rW),this.clearAllSnapshots();let e=R.now();g.delta=I(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rX),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nb(this.snapshot.measuredBox.x)||nb(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nA(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rC(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,a=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nN(this.latestValues)||a)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r8((t=r).x),r8(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nA();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nU(t.x,e.offset.x),nU(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nA();if(rj(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:a}=r;r!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&rj(t,e),nU(t.x,i.offset.x),nU(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nA();rj(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nF(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nN(r.latestValues)&&nF(n,r.latestValues)}return nN(this.latestValues)&&nF(n,this.latestValues),n}removeTransform(e){let t=nA();rj(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nN(n.latestValues))continue;nO(n.latestValues)&&n.updateSnapshot();let r=nA();rj(r,n.measurePageBox()),rA(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nN(this.latestValues)&&rA(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nA(),this.relativeTargetOrigin=nA(),nR(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rj(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nA(),this.targetWithTransforms=nA()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var a,s,o;this.forceRelativeParentToResolveTarget(),a=this.target,s=this.relativeTarget,o=this.relativeParent.target,nj(a.x,s.x,o.x),nj(a.y,s.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rj(this.target,this.layout.layoutBox),nI(this.target,this.targetDelta)):rj(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nA(),this.relativeTargetOrigin=nA(),nR(this.relativeTargetOrigin,this.target,e.target),rj(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&rU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nO(this.parent.latestValues)||nk(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rj(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,a,s=n.length;if(s){t.x=t.y=1;for(let o=0;o<s;o++){a=(i=n[o]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nF(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,nI(e,a)),r&&nN(i.latestValues)&&nF(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nA());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rE(this.prevProjectionDelta.x,this.projectionDelta.x),rE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nw(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===a&&this.treeScale.y===s&&rL(this.projectionDelta.x,this.prevProjectionDelta.x)&&rL(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),h.value&&rU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=n_(),this.projectionDelta=n_(),this.projectionDeltaWithTransform=n_()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},a={...this.latestValues},s=n_();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=nA(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r5));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r3(s.x,e.x,r),r3(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,g;nR(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=o,g=r,r4(f.x,p.x,m.x,g),r4(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,h=n,rO(u.x,h.x)&&rO(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=nA()),rj(n,this.relativeTarget)}l&&(this.animationValues=a,function(e,t,n,r,i,a){i?(e.opacity=eT(0,n.opacity??1,rx(r)),e.opacityExit=eT(t.opacity??1,0,rb(r))):a&&(e.opacity=eT(t.opacity??1,n.opacity??1,r));for(let i=0;i<rm;i++){let a=`border${rp[i]}Radius`,s=rv(t,a),o=rv(n,a);(void 0!==s||void 0!==o)&&(s||(s=0),o||(o=0),0===s||0===o||ry(s)===ry(o)?(e[a]=Math.max(eT(rg(s),rg(o),r),0),(el.test(o)||el.test(s))&&(e[a]+="%")):e[a]=o)}(t.rotate||n.rotate)&&(e.rotate=eT(t.rotate||0,n.rotate||0,r))}(a,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{rn.hasAnimatedSinceResize=!0,F.layout++,this.motionValue||(this.motionValue=A(0)),this.currentAnimation=function(e,t,n){let r=C(e)?e:A(e);return r.start(t6("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{F.layout--},onComplete:()=>{F.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nA();let t=nb(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nb(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rj(t,n),nF(t,i),nw(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rI),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rH("z",e,r,this.animationValues);for(let t=0;t<rV.length;t++)rH(`rotate${rV[t]}`,e,r,this.animationValues),rH(`skew${rV[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rF;let t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rf(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rf(e?.pointerEvents)||""),this.hasProjected&&!nN(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="",i=e.x.translate/t.x,a=e.y.translate/t.y,s=n?.z||0;if((i||a||s)&&(r=`translate3d(${i}px, ${a}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:s,skewY:o}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),a&&(r+=`rotateY(${a}deg) `),s&&(r+=`skewX(${s}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:a,y:s}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*s.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ra){if(void 0===i[e])continue;let{correct:n,applyTo:a,isCSSVariable:s}=ra[e],o="none"===t.transform?i[e]:n(i[e],r);if(a){let e=a.length;for(let n=0;n<e;n++)t[a[n]]=o}else s?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=r===this?rf(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rG),this.root.sharedNodes.clear()}}}function rz(e){e.updateLayout()}function rW(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,a=t.source!==e.layout.source;"size"===i?nM(e=>{let r=a?t.measuredBox[e]:t.layoutBox[e],i=nb(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&nM(r=>{let i=a?t.measuredBox[r]:t.layoutBox[r],s=nb(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=n_();nw(s,n,t.layoutBox);let o=n_();a?nw(o,e.applyTransform(r,!0),t.measuredBox):nw(o,n,t.layoutBox);let l=!rC(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:a}=r;if(i&&a){let s=nA();nR(s,t.layoutBox,i.layoutBox);let o=nA();nR(o,n,a.layoutBox),rk(s,o)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:o,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rK(e){h.value&&rU.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rq(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rX(e){e.clearSnapshot()}function rG(e){e.clearMeasurements()}function rY(e){e.isLayoutDirty=!1}function rQ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rZ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rJ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r3(e,t,n){e.translate=eT(t.translate,0,n),e.scale=eT(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r4(e,t,n,r){e.min=eT(t.min,n.min,r),e.max=eT(t.max,n.max,r)}function r5(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r6={duration:.45,ease:[.4,0,.1,1]},r7=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r9=r7("applewebkit/")&&!r7("chrome/")?Math.round:u;function r8(e){e.min=r9(e.min),e.max=r9(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rD(t)-rD(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=r$({attachResizeListener:(e,t)=>np(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},ia=r$({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});var is=n(9292);function io(e,t){let n=(0,is.K)(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function il(e){return!("touch"===e.pointerType||nf.x||nf.y)}function iu(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&p.postRender(()=>i(t,ng(t)))}class ic extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=io(e,n),s=e=>{if(!il(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let a=e=>{il(e)&&(r(e),n.removeEventListener("pointerleave",a))};n.addEventListener("pointerleave",a,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),a}(e,(e,t)=>(iu(this.node,t,"Start"),e=>iu(this.node,e,"End"))))}unmount(){}}class id extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=L(np(this.node.current,"focus",()=>this.onFocus()),np(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ih=(e,t)=>!!t&&(e===t||ih(e,t.parentElement)),ip=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),im=new WeakSet;function ig(e){return t=>{"Enter"===t.key&&e(t)}}function iy(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iv=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=ig(()=>{if(im.has(n))return;iy(n,"down");let e=ig(()=>{iy(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>iy(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function ix(e){return nm(e)&&!(nf.x||nf.y)}function ib(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&p.postRender(()=>i(t,ng(t)))}class iP extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=io(e,n),s=e=>{let r=e.currentTarget;if(!ix(e))return;im.add(r);let a=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),im.has(r)&&im.delete(r),ix(e)&&"function"==typeof a&&a(e,{success:t})},o=e=>{s(e,r===window||r===document||n.useGlobalTarget||ih(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",o,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),(0,tY.s)(e))&&(e.addEventListener("focus",e=>iv(e,i)),ip.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),a}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iw=new WeakMap,ij=new WeakMap,iE=e=>{let t=iw.get(e.target);t&&t(e)},iR=e=>{e.forEach(iE)},iT={some:0,all:1};class i_ extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,a={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iT[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ij.has(n)||ij.set(n,{});let r=ij.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iR,{root:e,...t})),r[i]}(t);return iw.set(e,n),r.observe(e),()=>{iw.delete(e),r.unobserve(e)}}(this.node.current,a,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),a=t?n:r;a&&a(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iS=(0,n9.createContext)({strict:!1});var iA=n(2582);let iM=(0,n9.createContext)({});function iC(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function iO(e){return!!(iC(e)||e.variants)}function iN(e){return Array.isArray(e)?e.join(" "):e}var ik=n(7044);let iD={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iL={};for(let e in iD)iL[e]={isEnabled:t=>iD[e].some(e=>!!t[e])};let iI=Symbol.for("motionComponentSymbol");var iU=n(1279),iV=n(2743);function iF(e,{layout:t,layoutId:n}){return x.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ra[e]||"opacity"===e)}let iB=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iH={...X,transform:Math.round},i$={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:eo,rotateX:eo,rotateY:eo,rotateZ:eo,scale:Y,scaleX:Y,scaleY:Y,scaleZ:Y,skew:eo,skewX:eo,skewY:eo,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:G,originX:eh,originY:eh,originZ:eu,zIndex:iH,fillOpacity:G,strokeOpacity:G,numOctaves:iH},iz={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iW=v.length;function iK(e,t,n){let{style:r,vars:i,transformOrigin:a}=e,s=!1,o=!1;for(let e in t){let n=t[e];if(x.has(e)){s=!0;continue}if(z(e)){i[e]=n;continue}{let t=iB(n,i$[e]);e.startsWith("origin")?(o=!0,a[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let a=0;a<iW;a++){let s=v[a],o=e[s];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!s.startsWith("scale"):0===parseFloat(o))||n){let e=iB(o,i$[s]);if(!l){i=!1;let t=iz[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin=`${e} ${t} ${n}`}}let iq=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iX(e,t,n){for(let r in t)C(t[r])||iF(r,n)||(e[r]=t[r])}let iG={offset:"stroke-dashoffset",array:"stroke-dasharray"},iY={offset:"strokeDashoffset",array:"strokeDasharray"};function iQ(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:a=1,pathOffset:s=0,...o},l,u,c){if(iK(e,o,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let a=i?iG:iY;e[a.offset]=eu.transform(-r);let s=eu.transform(t),o=eu.transform(n);e[a.array]=`${s} ${o}`}(d,i,a,s,!1)}let iZ=()=>({...iq(),attrs:{}}),iJ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),i0=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i1(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||i0.has(e)}let i2=e=>!i1(e);try{!function(e){e&&(i2=t=>t.startsWith("on")?!i1(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i3=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i4(e){if("string"!=typeof e||e.includes("-"));else if(i3.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i5=n(2789);let i6=e=>(t,n)=>{let r=(0,n9.useContext)(iM),a=(0,n9.useContext)(iU.t),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,a){return{latestValues:function(e,t,n,r){let a={},o=r(e,{});for(let e in o)a[e]=rf(o[e]);let{initial:l,animate:u}=e,c=iC(e),d=iO(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!n&&!1===n.initial,f=(h=h||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=s(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(n,r,a,e),renderState:t()}})(e,t,r,a);return n?o():(0,i5.M)(o)};function i7(e,t,n){let{style:r}=e,i={};for(let a in r)(C(r[a])||t.style&&C(t.style[a])||iF(a,e)||n?.getValue(a)?.liveStyle!==void 0)&&(i[a]=r[a]);return i}let i9={useVisualState:i6({scrapeMotionValuesFromProps:i7,createRenderState:iq})};function i8(e,t,n){let r=i7(e,t,n);for(let n in e)(C(e[n])||C(t[n]))&&(r[-1!==v.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let ae={useVisualState:i6({scrapeMotionValuesFromProps:i8,createRenderState:iZ})},at=e=>t=>t.test(e),an=[X,eu,el,eo,ed,ec,{test:e=>"auto"===e,parse:e=>e}],ar=e=>an.find(at(e)),ai=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),aa=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,as=e=>/^0[^.\s]+$/u.test(e),ao=new Set(["brightness","contrast","saturate","opacity"]);function al(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Z)||[];if(!r)return e;let i=n.replace(r,""),a=+!!ao.has(t);return r!==n&&(a*=100),t+"("+a+i+")"}let au=/\b([a-z-]*)\(.*?\)/gu,ac={...ej,getAnimatableNone:e=>{let t=e.match(au);return t?t.map(al).join(" "):e}},ad={...i$,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:ac,WebkitFilter:ac},ah=e=>ad[e];function af(e,t){let n=ah(e);return n!==ac&&(n=ej),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let ap=new Set(["auto","none","0"]);class am extends tD{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&K(r=r.trim())){let i=function e(t,n,r=1){H(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,a]=function(e){let t=aa.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return ai(e)?parseFloat(e):e}return K(a)?e(a,n,r+1):a}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!b.has(n)||2!==e.length)return;let[r,i]=e,a=ar(r),s=ar(i);if(a!==s)if(tR(a)&&tR(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tS[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||as(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!ap.has(t)&&ex(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=af(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tS[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,a=n[i];n[i]=tS[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let ag=[...an,ep,ej],ay=e=>ag.find(at(e)),av={current:null},ax={current:!1},ab=new WeakMap,aP=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class aw{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:a},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tD,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=R.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=a;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iC(t),this.isVariantNode=iO(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&C(t)&&t.set(o[e],!1)}}mount(e){this.current=e,ab.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ax.current||function(){if(ax.current=!0,ik.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>av.current=e.matches;e.addListener(t),t()}else av.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||av.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=x.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),a(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iL){let t=iL[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nA()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<aP.length;t++){let n=aP[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],a=n[r];if(C(i))e.addValue(r,i);else if(C(a))e.addValue(r,A(i,{owner:e}));else if(a!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,A(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=A(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(ai(n)||as(n))?n=parseFloat(n):!ay(n)&&ej.test(t)&&(n=af(e,t)),this.setBaseTarget(e,C(n)?n.get():n)),C(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=s(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||C(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new j),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class aj extends aw{constructor(){super(...arguments),this.KeyframeResolver=am}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;C(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function aE(e,{style:t,vars:n},r,i){for(let a in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(a,n[a])}class aR extends aj{constructor(){super(...arguments),this.type="html",this.renderInstance=aE}readValueFromInstance(e,t){if(x.has(t))return this.projection?.isProjecting?tP(t):tj(e,t);{let n=window.getComputedStyle(e),r=(z(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nB(e,t)}build(e,t,n){iK(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i7(e,t,n)}}let aT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class a_ extends aj{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nA}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(x.has(t)){let e=ah(t);return e&&e.default||0}return t=aT.has(t)?t:N(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i8(e,t,n)}build(e,t,n){iQ(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in aE(e,t,void 0,r),t.attrs)e.setAttribute(aT.has(n)?n:N(n),t.attrs[n])}mount(e){this.isSVGTag=iJ(e.tagName),super.mount(e)}}let aS=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tX={animation:{Feature:nc},exit:{Feature:nh},inView:{Feature:i_},tap:{Feature:iP},focus:{Feature:id},hover:{Feature:ic},pan:{Feature:n5},drag:{Feature:n3,ProjectionNode:ia,MeasureLayout:ro},layout:{ProjectionNode:ia,MeasureLayout:ro}},tG=(e,t)=>i4(e)?new a_(t):new aR(t,{allowProjection:e!==n9.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){function a(e,a){var s,o,l;let u,c={...(0,n9.useContext)(iA.Q),...e,layoutId:function({layoutId:e}){let t=(0,n9.useContext)(re.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:n}=function(e,t){if(iC(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n9.useContext)(iM));return(0,n9.useMemo)(()=>({initial:t,animate:n}),[iN(t),iN(n)])}(e),f=r(e,d);if(!d&&ik.B){o=0,l=0,(0,n9.useContext)(iS).strict;let e=function(e){let{drag:t,layout:n}=iL;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,n,r,i){let{visualElement:a}=(0,n9.useContext)(iM),s=(0,n9.useContext)(iS),o=(0,n9.useContext)(iU.t),l=(0,n9.useContext)(iA.Q).reducedMotion,u=(0,n9.useRef)(null);r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:a,props:n,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let c=u.current,d=(0,n9.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:a,drag:s,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:!!s||o&&n$(o),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,d);let h=(0,n9.useRef)(!1);(0,n9.useInsertionEffect)(()=>{c&&h.current&&c.update(n,o)});let f=n[k],p=(0,n9.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,iV.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n7.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,n9.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),c}(i,f,c,t,e.ProjectionNode)}return(0,n6.jsxs)(iM.Provider,{value:h,children:[u&&h.visualElement?(0,n6.jsx)(u,{visualElement:h.visualElement,...c}):null,n(i,e,(s=h.visualElement,(0,n9.useCallback)(e=>{e&&f.onMount&&f.onMount(e),s&&(e?s.mount(e):s.unmount()),a&&("function"==typeof a?a(e):n$(a)&&(a.current=e))},[s])),f,d,h.visualElement)]})}e&&function(e){for(let t in e)iL[t]={...iL[t],...e[t]}}(e),a.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let s=(0,n9.forwardRef)(a);return s[iI]=i,s}({...i4(e)?ae:i9,preloadedFeatures:tX,useRender:function(e=!1){return(t,n,r,{latestValues:i},a)=>{let s=(i4(t)?function(e,t,n,r){let i=(0,n9.useMemo)(()=>{let n=iZ();return iQ(n,t,iJ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iX(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iX(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n9.useMemo)(()=>{let n=iq();return iK(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,a,t),o=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i2(i)||!0===n&&i1(i)||!t&&!i1(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n9.Fragment?{...o,...s,ref:r}:{},{children:u}=n,c=(0,n9.useMemo)(()=>C(u)?u.get():u,[u]);return(0,n9.createElement)(t,{...l,children:c})}}(t),createVisualElement:tG,Component:e})}))}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,999],()=>n(2675));module.exports=r})();