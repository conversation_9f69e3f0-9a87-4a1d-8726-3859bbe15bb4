'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import Link from 'next/link';

const Pricing = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
      },
    },
  };

  const plans = [
    {
      name: 'Part-time',
      price: '$2450',
      period: '/ month',
      features: [
        'Fast delivery & response',
        'Pause / cancel anytime',
        'Dedicated project manager',
        '1 request at the time',
        'SEO Marketing',
      ],
      popular: false,
      href: 'https://stylokit.lemonsqueezy.com/buy/e842c934-9e86-4005-9608-8a025089e6a4',
    },
    {
      name: 'Full-time',
      price: '$3950',
      period: '/ month',
      features: [
        'Fast delivery & response',
        'Pause / cancel anytime',
        'Dedicated project manager',
        '10 request at the time',
        'E-commerce integration',
      ],
      popular: false,
      href: 'https://stylokit.lemonsqueezy.com/buy/e842c934-9e86-4005-9608-8a025089e6a4',
    },
    {
      name: 'Custom',
      price: '$5999',
      period: '/ month',
      features: [
        'Fast delivery & response',
        'Pause / cancel anytime',
        'Dedicated project manager',
        'Unlimited request',
        'E-commerce integration',
      ],
      popular: true,
      href: 'https://stylokit.lemonsqueezy.com/buy/e842c934-9e86-4005-9608-8a025089e6a4',
    },
  ];

  return (
    <section ref={ref} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? 'visible' : 'hidden'}
          className="text-center mb-16"
        >
          <motion.span
            variants={itemVariants}
            className="text-sm font-medium text-gray-500 uppercase tracking-wider"
          >
            Simple Pricing
          </motion.span>
          <motion.h2
            variants={itemVariants}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mt-4"
          >
            Unlock Your Growth
          </motion.h2>
        </motion.div>

        {/* Pricing Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? 'visible' : 'hidden'}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              variants={itemVariants}
              whileHover={{ y: -10, scale: 1.02 }}
              transition={{ duration: 0.3 }}
              className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 ${
                plan.popular ? 'ring-2 ring-black' : ''
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-black text-white px-4 py-2 rounded-full text-sm font-medium">
                    Popular
                  </span>
                </div>
              )}

              <div className="p-8">
                {/* Plan Name */}
                <h3 className="text-xl font-bold font-display text-black mb-4">
                  {plan.name}
                </h3>

                {/* Price */}
                <div className="mb-8">
                  <span className="text-4xl font-bold text-black">
                    {plan.price}
                  </span>
                  <span className="text-gray-600 ml-2">
                    {plan.period}
                  </span>
                </div>

                {/* Features */}
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <svg
                        className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    href={plan.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`block w-full text-center py-3 px-6 rounded-full font-medium transition-all duration-300 ${
                      plan.popular
                        ? 'bg-black text-white hover:bg-gray-800'
                        : 'bg-gray-100 text-black hover:bg-gray-200'
                    }`}
                  >
                    Get Started
                  </Link>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Info */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={isInView ? 'visible' : 'hidden'}
          className="text-center mt-12"
        >
          <p className="text-gray-600">
            All plans include 24/7 support and a 30-day money-back guarantee.
          </p>
          <p className="text-gray-600 mt-2">
            Need a custom solution?{' '}
            <Link
              href="/contact"
              className="text-black font-medium hover:underline"
            >
              Contact us
            </Link>
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default Pricing;
