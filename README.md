# Panda Patronage - Digital Marketing Agency Website

A modern, responsive website clone of the Panda Patronage digital marketing agency, built with Next.js, React, TypeScript, Tailwind CSS, and Framer Motion.

## 🚀 Features

- **Modern Design**: Clean, professional design with smooth animations
- **Responsive**: Fully responsive design that works on all devices
- **Animations**: Beautiful animations powered by Framer Motion
- **Performance**: Optimized for speed with Next.js 15
- **TypeScript**: Full TypeScript support for better development experience
- **Tailwind CSS 4**: Latest version of Tailwind CSS for styling

## 🛠️ Tech Stack

- **Framework**: Next.js 15.3.3
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **Animations**: Framer Motion 12.15.0
- **Fonts**: Inter & Poppins from Google Fonts
- **Package Manager**: npm

## 📦 Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd panda-patronage
```

2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
panda-patronage/
├── components/
│   ├── Header.tsx          # Navigation header with mobile menu
│   ├── Hero.tsx            # Hero section with animated elements
│   ├── Services.tsx        # Services showcase section
│   ├── Portfolio.tsx       # Portfolio/projects section
│   ├── Testimonials.tsx    # Customer testimonials
│   ├── Pricing.tsx         # Pricing plans section
│   ├── FAQ.tsx             # Frequently asked questions
│   └── Footer.tsx          # Footer with links and social media
├── src/
│   └── app/
│       ├── globals.css     # Global styles and Tailwind imports
│       ├── layout.tsx      # Root layout with fonts and metadata
│       └── page.tsx        # Main homepage
└── public/                 # Static assets
```

## 🎨 Components Overview

### Header

- Fixed navigation with scroll effects
- Mobile-responsive hamburger menu
- Smooth animations on load

### Hero

- Large hero section with animated text
- Floating background elements
- Call-to-action buttons
- Scroll indicator

### Services

- Two-section layout: process phases and professional services
- Hover animations and cards
- Icon-based service representation

### Portfolio

- Grid layout of featured projects
- External links to live projects
- Animated project cards with tags

### Testimonials

- Carousel-style testimonials
- Star ratings and customer info
- Brand logos section

### Pricing

- Three-tier pricing structure
- Popular plan highlighting
- Feature comparison lists

### FAQ

- Expandable accordion-style questions
- Smooth open/close animations
- Contact support CTA

### Footer

- Company and legal links
- Social media icons
- Brand information

## 🚀 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📱 Responsive Design

The website is fully responsive and optimized for:

- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🎭 Animations

Powered by Framer Motion with:

- Page load animations
- Scroll-triggered animations
- Hover effects
- Smooth transitions
- Floating elements

## 🌐 Original Website

This is a clone of: [https://gracious-population-676989.framer.app/](https://gracious-population-676989.framer.app/)

## 📄 License

This project is for educational purposes only.
