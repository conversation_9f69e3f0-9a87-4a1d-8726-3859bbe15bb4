"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";

const Testimonials = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const testimonials = [
    {
      name: "Elite Fitness Co.",
      role: "Owner",
      content:
        "Working with Panda Patronage transformed our brand&apos;s online presence. Their innovative strategies increased our website traffic by 45% in just 3 months!",
      avatar: "/api/placeholder/60/60",
      rating: 5,
    },
    {
      name: "GreenTech Solutions",
      role: "Manager",
      content:
        "Panda Patronage took our ad campaigns to the next level. Their full-funnel approach helped us achieve a 200% ROI on our marketing spend.",
      avatar: "/api/placeholder/60/60",
      rating: 5,
    },
    {
      name: "Harmony Skin Care",
      role: "CEO",
      content:
        "We struggled with brand consistency across platforms until their team created a cohesive strategy that boosted customer engagement.",
      avatar: "/api/placeholder/60/60",
      rating: 5,
    },
  ];

  const brands = [
    { name: "Brand 1", logo: "/api/placeholder/120/60" },
    { name: "Brand 2", logo: "/api/placeholder/120/60" },
    { name: "Brand 3", logo: "/api/placeholder/120/60" },
    { name: "Brand 4", logo: "/api/placeholder/120/60" },
    { name: "Brand 5", logo: "/api/placeholder/120/60" },
    { name: "Brand 6", logo: "/api/placeholder/120/60" },
    { name: "Brand 7", logo: "/api/placeholder/120/60" },
    { name: "Brand 8", logo: "/api/placeholder/120/60" },
  ];

  return (
    <section ref={ref} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.span
            variants={itemVariants}
            className="text-sm font-medium text-gray-500 uppercase tracking-wider"
          >
            Testimonials
          </motion.span>
          <motion.h2
            variants={itemVariants}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mt-4 mb-6"
          >
            Our satisfied customers
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-lg text-gray-600 max-w-3xl mx-auto"
          >
            Panda Patronage empowers teams with seamless Strategies and
            time-saving Solutions. Discover client success!
          </motion.p>
        </motion.div>

        {/* Testimonials Carousel */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="mb-20"
        >
          <div className="relative max-w-4xl mx-auto">
            <motion.div
              variants={itemVariants}
              className="bg-gray-50 rounded-3xl p-8 md:p-12"
            >
              <div className="text-center">
                {/* Stars */}
                <div className="flex justify-center mb-6">
                  {[...Array(5)].map((_, i) => (
                    <motion.svg
                      key={i}
                      className="w-6 h-6 text-yellow-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: i * 0.1 + 0.5 }}
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </motion.svg>
                  ))}
                </div>

                {/* Testimonial Content */}
                <motion.blockquote
                  key={activeTestimonial}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  className="text-xl md:text-2xl font-medium text-gray-800 mb-8 leading-relaxed"
                >
                  "{testimonials[activeTestimonial].content}"
                </motion.blockquote>

                {/* Author */}
                <div className="flex items-center justify-center space-x-4">
                  <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-gray-600 font-medium">
                      {testimonials[activeTestimonial].name.charAt(0)}
                    </span>
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-gray-800">
                      {testimonials[activeTestimonial].role}
                    </p>
                    <p className="text-gray-600">
                      – {testimonials[activeTestimonial].name}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Navigation Dots */}
            <div className="flex justify-center mt-8 space-x-3">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === activeTestimonial
                      ? "bg-black scale-125"
                      : "bg-gray-300 hover:bg-gray-400"
                  }`}
                />
              ))}
            </div>
          </div>
        </motion.div>

        {/* Brand Logos */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="border-t border-gray-200 pt-16"
        >
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60"
          >
            {brands.map((brand, index) => (
              <motion.div
                key={brand.name}
                variants={itemVariants}
                whileHover={{ scale: 1.1, opacity: 1 }}
                transition={{ duration: 0.2 }}
                className="flex items-center justify-center"
              >
                <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 font-medium text-sm">
                    {brand.name}
                  </span>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;
