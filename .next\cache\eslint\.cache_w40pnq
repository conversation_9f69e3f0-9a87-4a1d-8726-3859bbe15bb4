[{"C:\\laragon\\www\\panda-patronage\\components\\FAQ.tsx": "1", "C:\\laragon\\www\\panda-patronage\\components\\Footer.tsx": "2", "C:\\laragon\\www\\panda-patronage\\components\\Header.tsx": "3", "C:\\laragon\\www\\panda-patronage\\components\\Hero.tsx": "4", "C:\\laragon\\www\\panda-patronage\\components\\Portfolio.tsx": "5", "C:\\laragon\\www\\panda-patronage\\components\\Pricing.tsx": "6", "C:\\laragon\\www\\panda-patronage\\components\\Services.tsx": "7", "C:\\laragon\\www\\panda-patronage\\components\\Testimonials.tsx": "8", "C:\\laragon\\www\\panda-patronage\\src\\app\\layout.tsx": "9", "C:\\laragon\\www\\panda-patronage\\src\\app\\page.tsx": "10"}, {"size": 5928, "mtime": 1748766251651, "results": "11", "hashOfConfig": "12"}, {"size": 7106, "mtime": 1748766010374, "results": "13", "hashOfConfig": "12"}, {"size": 5181, "mtime": 1748765848389, "results": "14", "hashOfConfig": "12"}, {"size": 6221, "mtime": 1748765868274, "results": "15", "hashOfConfig": "12"}, {"size": 8065, "mtime": 1748765915831, "results": "16", "hashOfConfig": "12"}, {"size": 6666, "mtime": 1748765961630, "results": "17", "hashOfConfig": "12"}, {"size": 7138, "mtime": 1748766268385, "results": "18", "hashOfConfig": "12"}, {"size": 7707, "mtime": 1748766286727, "results": "19", "hashOfConfig": "12"}, {"size": 823, "mtime": 1748765811259, "results": "20", "hashOfConfig": "12"}, {"size": 662, "mtime": 1748766026528, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ccv8qb", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\laragon\\www\\panda-patronage\\components\\FAQ.tsx", ["52"], [], "C:\\laragon\\www\\panda-patronage\\components\\Footer.tsx", [], [], "C:\\laragon\\www\\panda-patronage\\components\\Header.tsx", ["53", "54", "55"], [], "C:\\laragon\\www\\panda-patronage\\components\\Hero.tsx", ["56", "57", "58"], [], "C:\\laragon\\www\\panda-patronage\\components\\Portfolio.tsx", ["59"], [], "C:\\laragon\\www\\panda-patronage\\components\\Pricing.tsx", ["60"], [], "C:\\laragon\\www\\panda-patronage\\components\\Services.tsx", ["61", "62"], [], "C:\\laragon\\www\\panda-patronage\\components\\Testimonials.tsx", ["63", "64", "65"], [], "C:\\laragon\\www\\panda-patronage\\src\\app\\layout.tsx", [], [], "C:\\laragon\\www\\panda-patronage\\src\\app\\page.tsx", [], [], {"ruleId": "66", "severity": 2, "message": "67", "line": 165, "column": 37, "nodeType": "68", "messageId": "69", "suggestions": "70"}, {"ruleId": "71", "severity": 2, "message": "72", "line": 5, "column": 8, "nodeType": null, "messageId": "73", "endLine": 5, "endColumn": 13}, {"ruleId": "66", "severity": 2, "message": "67", "line": 87, "column": 18, "nodeType": "68", "messageId": "69", "suggestions": "74"}, {"ruleId": "66", "severity": 2, "message": "67", "line": 143, "column": 20, "nodeType": "68", "messageId": "69", "suggestions": "75"}, {"ruleId": "71", "severity": 2, "message": "72", "line": 5, "column": 8, "nodeType": null, "messageId": "73", "endLine": 5, "endColumn": 13}, {"ruleId": "66", "severity": 2, "message": "67", "line": 78, "column": 15, "nodeType": "68", "messageId": "69", "suggestions": "76"}, {"ruleId": "66", "severity": 2, "message": "67", "line": 103, "column": 15, "nodeType": "68", "messageId": "69", "suggestions": "77"}, {"ruleId": "71", "severity": 2, "message": "78", "line": 120, "column": 35, "nodeType": null, "messageId": "73", "endLine": 120, "endColumn": 40}, {"ruleId": "71", "severity": 2, "message": "78", "line": 111, "column": 29, "nodeType": null, "messageId": "73", "endLine": 111, "endColumn": 34}, {"ruleId": "71", "severity": 2, "message": "78", "line": 116, "column": 37, "nodeType": null, "messageId": "73", "endLine": 116, "endColumn": 42}, {"ruleId": "71", "severity": 2, "message": "78", "line": 170, "column": 49, "nodeType": null, "messageId": "73", "endLine": 170, "endColumn": 54}, {"ruleId": "66", "severity": 2, "message": "79", "line": 142, "column": 19, "nodeType": "68", "messageId": "69", "suggestions": "80"}, {"ruleId": "66", "severity": 2, "message": "79", "line": 142, "column": 61, "nodeType": "68", "messageId": "69", "suggestions": "81"}, {"ruleId": "71", "severity": 2, "message": "78", "line": 192, "column": 33, "nodeType": null, "messageId": "73", "endLine": 192, "endColumn": 38}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["82", "83", "84", "85"], "@typescript-eslint/no-unused-vars", "'Image' is defined but never used.", "unusedVar", ["86", "87", "88", "89"], ["90", "91", "92", "93"], ["94", "95", "96", "97"], ["98", "99", "100", "101"], "'index' is defined but never used.", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["102", "103", "104", "105"], ["106", "107", "108", "109"], {"messageId": "110", "data": "111", "fix": "112", "desc": "113"}, {"messageId": "110", "data": "114", "fix": "115", "desc": "116"}, {"messageId": "110", "data": "117", "fix": "118", "desc": "119"}, {"messageId": "110", "data": "120", "fix": "121", "desc": "122"}, {"messageId": "110", "data": "123", "fix": "124", "desc": "113"}, {"messageId": "110", "data": "125", "fix": "126", "desc": "116"}, {"messageId": "110", "data": "127", "fix": "128", "desc": "119"}, {"messageId": "110", "data": "129", "fix": "130", "desc": "122"}, {"messageId": "110", "data": "131", "fix": "132", "desc": "113"}, {"messageId": "110", "data": "133", "fix": "134", "desc": "116"}, {"messageId": "110", "data": "135", "fix": "136", "desc": "119"}, {"messageId": "110", "data": "137", "fix": "138", "desc": "122"}, {"messageId": "110", "data": "139", "fix": "140", "desc": "113"}, {"messageId": "110", "data": "141", "fix": "142", "desc": "116"}, {"messageId": "110", "data": "143", "fix": "144", "desc": "119"}, {"messageId": "110", "data": "145", "fix": "146", "desc": "122"}, {"messageId": "110", "data": "147", "fix": "148", "desc": "113"}, {"messageId": "110", "data": "149", "fix": "150", "desc": "116"}, {"messageId": "110", "data": "151", "fix": "152", "desc": "119"}, {"messageId": "110", "data": "153", "fix": "154", "desc": "122"}, {"messageId": "110", "data": "155", "fix": "156", "desc": "157"}, {"messageId": "110", "data": "158", "fix": "159", "desc": "160"}, {"messageId": "110", "data": "161", "fix": "162", "desc": "163"}, {"messageId": "110", "data": "164", "fix": "165", "desc": "166"}, {"messageId": "110", "data": "167", "fix": "168", "desc": "157"}, {"messageId": "110", "data": "169", "fix": "170", "desc": "160"}, {"messageId": "110", "data": "171", "fix": "172", "desc": "163"}, {"messageId": "110", "data": "173", "fix": "174", "desc": "166"}, "replaceWithAlt", {"alt": "175"}, {"range": "176", "text": "177"}, "Replace with `&apos;`.", {"alt": "178"}, {"range": "179", "text": "180"}, "Replace with `&lsquo;`.", {"alt": "181"}, {"range": "182", "text": "183"}, "Replace with `&#39;`.", {"alt": "184"}, {"range": "185", "text": "186"}, "Replace with `&rsquo;`.", {"alt": "175"}, {"range": "187", "text": "188"}, {"alt": "178"}, {"range": "189", "text": "190"}, {"alt": "181"}, {"range": "191", "text": "192"}, {"alt": "184"}, {"range": "193", "text": "194"}, {"alt": "175"}, {"range": "195", "text": "196"}, {"alt": "178"}, {"range": "197", "text": "198"}, {"alt": "181"}, {"range": "199", "text": "200"}, {"alt": "184"}, {"range": "201", "text": "202"}, {"alt": "175"}, {"range": "203", "text": "204"}, {"alt": "178"}, {"range": "205", "text": "206"}, {"alt": "181"}, {"range": "207", "text": "208"}, {"alt": "184"}, {"range": "209", "text": "210"}, {"alt": "175"}, {"range": "211", "text": "212"}, {"alt": "178"}, {"range": "213", "text": "214"}, {"alt": "181"}, {"range": "215", "text": "216"}, {"alt": "184"}, {"range": "217", "text": "218"}, {"alt": "219"}, {"range": "220", "text": "221"}, "Replace with `&quot;`.", {"alt": "222"}, {"range": "223", "text": "224"}, "Replace with `&ldquo;`.", {"alt": "225"}, {"range": "226", "text": "227"}, "Replace with `&#34;`.", {"alt": "228"}, {"range": "229", "text": "230"}, "Replace with `&rdquo;`.", {"alt": "219"}, {"range": "231", "text": "232"}, {"alt": "222"}, {"range": "233", "text": "234"}, {"alt": "225"}, {"range": "235", "text": "236"}, {"alt": "228"}, {"range": "237", "text": "238"}, "&apos;", [5013, 5078], "\n            Still have questions? We&apos;re here to help!\n          ", "&lsquo;", [5013, 5078], "\n            Still have questions? We&lsquo;re here to help!\n          ", "&#39;", [5013, 5078], "\n            Still have questions? We&#39;re here to help!\n          ", "&rsquo;", [5013, 5078], "\n            Still have questions? We&rsquo;re here to help!\n          ", [2930, 2968], "\n              Let&apos;s Talk\n            ", [2930, 2968], "\n              Let&lsquo;s Talk\n            ", [2930, 2968], "\n              Let&#39;s Talk\n            ", [2930, 2968], "\n              Let&rsquo;s Talk\n            ", [5007, 5049], "\n                Let&apos;s Talk\n              ", [5007, 5049], "\n                Let&lsquo;s Talk\n              ", [5007, 5049], "\n                Let&#39;s Talk\n              ", [5007, 5049], "\n                Let&rsquo;s Talk\n              ", [2082, 2100], "\n            We&apos;re", [2082, 2100], "\n            We&lsquo;re", [2082, 2100], "\n            We&#39;re", [2082, 2100], "\n            We&rsquo;re", [3052, 3197], "\n            We&apos;re here to help you see through the eyes of your audience, paving\n            the way for your success without limits.\n          ", [3052, 3197], "\n            We&lsquo;re here to help you see through the eyes of your audience, paving\n            the way for your success without limits.\n          ", [3052, 3197], "\n            We&#39;re here to help you see through the eyes of your audience, paving\n            the way for your success without limits.\n          ", [3052, 3197], "\n            We&rsquo;re here to help you see through the eyes of your audience, paving\n            the way for your success without limits.\n          ", "&quot;", [5027, 5047], "\n                  &quot;", "&ldquo;", [5027, 5047], "\n                  &ldquo;", "&#34;", [5027, 5047], "\n                  &#34;", "&rdquo;", [5027, 5047], "\n                  &rdquo;", [5088, 5106], "&quot;\n                ", [5088, 5106], "&ldquo;\n                ", [5088, 5106], "&#34;\n                ", [5088, 5106], "&rdquo;\n                "]