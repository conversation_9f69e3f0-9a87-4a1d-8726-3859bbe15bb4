'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';

const Footer = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  const companyLinks = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' },
    { name: 'Cases', href: '/cases' },
    { name: 'Article', href: '/article' },
  ];

  const legalLinks = [
    { name: 'Privacy Policy', href: '/legal/privacy-policy' },
    { name: 'Licensing', href: '/legal/licensing' },
    { name: 'Terms of Use', href: '/legal/terms-of-use' },
  ];

  const socialLinks = [
    {
      name: 'Twitter',
      href: 'https://x.com/Dmytri_Design',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
        </svg>
      ),
    },
    {
      name: 'Dribbble',
      href: 'https://dribbble.com/dmytriDesign',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 0C5.374 0 0 5.374 0 12s5.374 12 12 12 12-5.374 12-12S18.626 0 12 0zm7.568 5.302c1.4 1.5 2.252 3.5 2.273 5.698-.653-.126-7.512-1.5-7.512-1.5s-.126-.653-.378-1.4c3.274-1.274 5.617-3.798 5.617-3.798zM12 2.179c2.274 0 4.348.779 6.045 2.071 0 0-2.217 2.398-5.364 3.547C11.26 5.746 9.742 3.798 9.742 3.798 10.456 2.905 11.188 2.179 12 2.179zm-2.71 2.71s1.518 1.948 2.962 4.023c-3.798 1.022-7.134 1.022-7.134 1.022-.378-2.146.252-4.171 1.4-5.794.653.252 1.778.504 2.772.749zm-4.171 7.386s3.798 0 7.89-1.274c.252.504.378 1.022.63 1.526-4.674 1.274-7.134 4.8-7.134 4.8-1.526-1.652-2.398-3.924-1.386-5.052zm2.024 6.171s2.146-3.274 6.423-4.548c1.022 2.65 1.4 4.8 1.526 5.542-1.4.63-3.022.882-4.674.882-1.148 0-2.146-.252-3.275-.876zm6.045-.504c-.126-.504-.378-2.524-1.274-4.926 2.146-.378 4.023.252 4.023.252-.378 2.02-1.4 3.798-2.749 4.674z" />
        </svg>
      ),
    },
    {
      name: 'LinkedIn',
      href: 'https://www.linkedin.com/in/dmytri-design/',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
        </svg>
      ),
    },
  ];

  return (
    <footer className="bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="py-16"
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
            {/* Left Column - Logo and Description */}
            <motion.div variants={itemVariants}>
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                  <span className="text-black font-bold text-xl">P</span>
                </div>
                <span className="ml-3 text-xl font-bold font-display">
                  Panda Patronage
                </span>
              </div>
              <p className="text-gray-300 text-lg leading-relaxed max-w-md">
                Get in touch to find out more about digital experiences to effectively reach and engage customers and target audiences.
              </p>
            </motion.div>

            {/* Right Column - Links */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
              {/* Company Links */}
              <motion.div variants={itemVariants}>
                <h3 className="text-lg font-semibold mb-6">Company</h3>
                <ul className="space-y-4">
                  {companyLinks.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-gray-300 hover:text-white transition-colors duration-200"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Legal Links */}
              <motion.div variants={itemVariants}>
                <h3 className="text-lg font-semibold mb-6">Legal</h3>
                <ul className="space-y-4">
                  {legalLinks.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-gray-300 hover:text-white transition-colors duration-200"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Bottom Footer */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="border-t border-gray-800 py-8"
        >
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            {/* Copyright */}
            <motion.p
              variants={itemVariants}
              className="text-gray-400 text-sm"
            >
              © Panda-Patronage 2025
            </motion.p>

            {/* Social Links */}
            <motion.div
              variants={itemVariants}
              className="flex space-x-6"
            >
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.2, y: -2 }}
                  transition={{ duration: 0.2 }}
                  className="text-gray-400 hover:text-white transition-colors duration-200"
                >
                  {social.icon}
                </motion.a>
              ))}
            </motion.div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
