"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

const Services = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const services = [
    {
      title: "Planning Phase",
      description:
        "We map out the game plan upfront, deciding what needs to be done and when, so every step is clear and focused.",
      icon: "📋",
      color: "from-blue-400 to-blue-600",
    },
    {
      title: "Strategy Blueprint",
      description:
        "Once the plan is set, we craft a powerful strategy designed to drive your success and fuel your growth.",
      icon: "🎯",
      color: "from-purple-400 to-purple-600",
    },
    {
      title: "Working Process",
      description:
        "With the plan and strategy in place, our team dive in wholeheartedly to propel your success.",
      icon: "⚡",
      color: "from-orange-400 to-orange-600",
    },
  ];

  const professionalServices = [
    {
      title: "Branding",
      description:
        "Your company&apos;s identity hinges on branding. We assist in shaping a distinct voice and identity.",
      image: "/api/placeholder/300/200",
    },
    {
      title: "Social Media Management",
      description:
        "Your audience resides on social media, and we ensure your presence there! Our social media management services maintain your profiles",
      image: "/api/placeholder/300/200",
    },
    {
      title: "Meta Advertisement",
      description:
        "Target the right audience and maximize ROI with our Meta ad campaigns on Fb and Instagram.",
      image: "/api/placeholder/300/200",
    },
    {
      title: "Graphic Designing",
      description:
        "Our visual design solutions generate powerful imagery, from logos to promotional resources, that deliver your message effectively.",
      image: "/api/placeholder/300/200",
    },
    {
      title: "Web Development",
      description:
        "Your website is your digital storefront—We designed to captivate, engage, and drive traffic.",
      image: "/api/placeholder/300/200",
    },
  ];

  return (
    <section ref={ref} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* What Sets Us Apart Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.h2
            variants={itemVariants}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mb-6"
          >
            Explore What Sets Us Apart
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-lg text-gray-600 max-w-3xl mx-auto mb-16"
          >
            Explore how we stand out with innovative solutions designed to raise
            your fulfillment
          </motion.p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                variants={itemVariants}
                whileHover={{ y: -10, scale: 1.02 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
              >
                <div
                  className={`w-16 h-16 rounded-full bg-gradient-to-r ${service.color} flex items-center justify-center text-2xl mb-6 mx-auto`}
                >
                  {service.icon}
                </div>
                <h3 className="text-xl font-bold font-display text-black mb-4">
                  {service.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {service.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Professional Services Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="mt-32"
        >
          <div className="text-center mb-16">
            <motion.span
              variants={itemVariants}
              className="text-sm font-medium text-gray-500 uppercase tracking-wider"
            >
              Our services
            </motion.span>
            <motion.h2
              variants={itemVariants}
              className="text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mt-4 mb-6"
            >
              Professional Services That Showcase Our Expertise.
            </motion.h2>
            <motion.p
              variants={itemVariants}
              className="text-lg text-gray-600 max-w-3xl mx-auto"
            >
              From creative design to technical solutions, our services define
              industry excellence.
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {professionalServices.map((service, index) => (
              <motion.div
                key={service.title}
                variants={itemVariants}
                whileHover={{ y: -5 }}
                transition={{ duration: 0.3 }}
                className="group cursor-pointer"
              >
                <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                    <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-all duration-300" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-xl">🐼</span>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold font-display text-black mb-3">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
