"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import Link from "next/link";

const Portfolio = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const projects = [
    {
      title: "Codify",
      tags: ["Agency", "Portfolio", "Saas"],
      href: "https://codify.framer.website/",
      color: "from-blue-500 to-purple-600",
    },
    {
      title: "Taskify",
      tags: ["Business", "AI", "Saas"],
      href: "https://taskify.framer.website/",
      color: "from-green-500 to-teal-600",
    },
    {
      title: "Flexify",
      tags: ["Saas", "AI", "Business"],
      href: "https://flexify.framer.website/",
      color: "from-purple-500 to-pink-600",
    },
    {
      title: "Landify",
      tags: ["Business", "Portfolio", "Landing"],
      href: "https://landify.framer.website/",
      color: "from-orange-500 to-red-600",
    },
    {
      title: "Nexus AI",
      tags: ["AI", "Saas", "Business"],
      href: "https://nexusai.framer.website/",
      color: "from-cyan-500 to-blue-600",
    },
    {
      title: "Todofusion",
      tags: ["AI", "Business", "Agency"],
      href: "https://todofusion.framer.website/",
      color: "from-yellow-500 to-orange-600",
    },
  ];

  return (
    <section ref={ref} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Quote Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.p
            variants={itemVariants}
            className="text-2xl sm:text-3xl lg:text-4xl font-medium text-gray-700 max-w-5xl mx-auto leading-relaxed"
          >
            We have been creating projects that remain relevant today, tomorrow,
            and for decades to come
          </motion.p>
        </motion.div>

        {/* Portfolio Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold font-display text-black mb-6"
          >
            Building Digital Excellence with Panda.
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-lg text-gray-600 max-w-3xl mx-auto"
          >
            Discover the innovative marketing strategies that set Neutra apart,
            driving success in the digital landscape.
          </motion.p>
        </motion.div>

        {/* Portfolio Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {projects.map((project, index) => (
            <motion.div
              key={project.title}
              variants={itemVariants}
              whileHover={{ y: -10, scale: 1.02 }}
              transition={{ duration: 0.3 }}
              className="group"
            >
              <Link
                href={project.href}
                target="_blank"
                rel="noopener noreferrer"
                className="block"
              >
                <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                  {/* Project Image/Preview */}
                  <div
                    className={`h-64 bg-gradient-to-br ${project.color} relative overflow-hidden`}
                  >
                    <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-all duration-300" />

                    {/* Floating Elements */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        animate={{
                          y: [-5, 5, -5],
                          rotate: [-2, 2, -2],
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          ease: "easeInOut",
                        }}
                        className="w-20 h-20 bg-white/20 rounded-2xl backdrop-blur-sm flex items-center justify-center"
                      >
                        <span className="text-white font-bold text-2xl">
                          {project.title.charAt(0)}
                        </span>
                      </motion.div>
                    </div>

                    {/* Decorative Elements */}
                    <motion.div
                      animate={{
                        x: [-10, 10, -10],
                        y: [-5, 5, -5],
                      }}
                      transition={{
                        duration: 6,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                      className="absolute top-4 right-4 w-8 h-8 bg-white/30 rounded-full"
                    />
                    <motion.div
                      animate={{
                        x: [10, -10, 10],
                        y: [5, -5, 5],
                      }}
                      transition={{
                        duration: 5,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                      className="absolute bottom-4 left-4 w-6 h-6 bg-white/30 rounded-full"
                    />
                  </div>

                  {/* Project Info */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold font-display text-black mb-3 group-hover:text-gray-700 transition-colors duration-200">
                      {project.title}
                    </h3>

                    <div className="flex flex-wrap gap-2">
                      {project.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* View Project Arrow */}
                    <div className="mt-4 flex items-center text-gray-500 group-hover:text-black transition-colors duration-200">
                      <span className="text-sm font-medium">View Project</span>
                      <motion.svg
                        className="w-4 h-4 ml-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        initial={{ x: 0 }}
                        whileHover={{ x: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17 8l4 4m0 0l-4 4m4-4H3"
                        />
                      </motion.svg>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Portfolio;
